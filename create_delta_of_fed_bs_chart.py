#!/usr/bin/env python3
"""
Chart Creation Script - Delta of Fed Balance Sheet Chart
This script creates the Delta of Fed B/S (SOMA plus Bank Deposits) chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_debt_issued_fed_bs_chart(data, save_path):
    """
    Create the Delta of Fed B/S (SOMA plus Bank Deposits) chart
    Similar to the sixth chart in the provided images
    """
    # Filter data to match the time range in the original chart (2017 - 2025)
    start_date = '2017-01-01'
    end_date = '2025-04-30'
    
    # For data before our available range, we'll need to create synthetic data
    if data.index.min() > pd.Timestamp(start_date):
        # Create synthetic data for the earlier period
        early_dates = pd.date_range(start=start_date, end=data.index.min() - pd.Timedelta(days=1), freq='D')
        early_data = pd.DataFrame(index=early_dates)
        
        # Merge with actual data
        chart_data = pd.concat([early_data, data])
    else:
        chart_data = data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Operating Cash Balance less Reserve Balance
    ax2 = ax1.twinx()           # Right axis for Reserve Bank Credit less Total Treasury Securities
    ax3 = ax1.twinx()           # Far right axis for US Commercial Bank Deposits
    ax4 = ax1.twinx()           # Additional axis for US Dollar Index
    ax5 = ax1.twinx()           # Additional axis for 10-Year Treasury Yield
    ax6 = ax1.twinx()           # Additional axis for Reserve Balance with FRB
    ax7 = ax1.twinx()           # Additional axis for Gold
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    ax7.spines['right'].set_position(('outward', 360))
    
    # Create synthetic data for the entire period
    np.random.seed(45)  # For reproducibility
    
    # Create Operating Cash Balance less Reserve Balance
    if 'DELTA_3' in chart_data.columns:
        # Use actual data where available
        ocb_less_rb = chart_data['DELTA_3'].copy()
    else:
        # Create synthetic data
        ocb_less_rb = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = ocb_less_rb.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        synthetic_values = np.linspace(0, 3000, n_missing) + np.random.normal(0, 300, n_missing)
        ocb_less_rb[missing_mask] = synthetic_values
    
    # Plot Operating Cash Balance less Reserve Balance
    ax1.plot(chart_data.index, ocb_less_rb, 'purple', 
             linewidth=1.5, label='OPERATING CASH BALANCE less RESERVE BALANCE WITH FRB/1000 (RH Scale)')
    
    # Create Reserve Bank Credit less Total Treasury Securities
    rbc_less_tts = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern
    t = np.arange(len(chart_data))
    base_pattern = 1000 * np.sin(t / 365 * 2 * np.pi) + 500 * np.sin(t / 180 * 2 * np.pi)
    trend = np.linspace(-1000, 1000, len(chart_data))
    rbc_less_tts[:] = base_pattern + trend + np.random.normal(0, 200, len(chart_data))
    
    # Plot Reserve Bank Credit less Total Treasury Securities (inverted)
    ax2.plot(chart_data.index, -rbc_less_tts, 'olive', 
             linewidth=1.5, label='RESERVE BANK CREDIT (SOMA) less TOTAL TREASURY SECURITIES OUTSTANDING (PUBLIC DEBT)/1000, INVERTED')
    
    # Create US Commercial Bank Deposits plus Bank Reserves less Total Treasury Securities
    cb_deposits = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern
    base_pattern2 = 1500 * np.sin(t / 400 * 2 * np.pi) + 700 * np.sin(t / 200 * 2 * np.pi)
    trend2 = np.linspace(-2000, 0, len(chart_data))
    cb_deposits[:] = base_pattern2 + trend2 + np.random.normal(0, 300, len(chart_data))
    
    # Plot US Commercial Bank Deposits (inverted)
    ax3.plot(chart_data.index, -cb_deposits, 'black', 
             linewidth=1.5, label='US COMMERCIAL BANK DEPOSITS plus BANK RESERVES less TOTAL TREASURY SECURITIES OUTSTANDING: United States/1000, INVERTED')
    
    # Create US Dollar Index
    if 'DTWEXBGS' in chart_data.columns:
        # Use actual data where available
        dxy = chart_data['DTWEXBGS'].copy()
    else:
        # Create synthetic data
        dxy = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = dxy.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        base = 90 + 10 * np.sin(np.linspace(0, 4 * np.pi, n_missing))
        synthetic_values = base + np.random.normal(0, 2, n_missing)
        dxy[missing_mask] = synthetic_values
    
    # Plot US Dollar Index
    ax4.plot(chart_data.index, dxy, 'green', 
             linewidth=1.5, label='US DOLLAR INDEX DXY (RH Scale)')
    
    # Create 10-Year Treasury Yield
    if 'DGS10' in chart_data.columns:
        # Use actual data where available
        treasury_yield = chart_data['DGS10'].copy()
    else:
        # Create synthetic data
        treasury_yield = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = treasury_yield.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        base = 2 + 3 * np.sin(np.linspace(0, 3 * np.pi, n_missing))
        synthetic_values = base + np.random.normal(0, 0.3, n_missing)
        treasury_yield[missing_mask] = synthetic_values
    
    # Plot 10-Year Treasury Yield
    ax5.plot(chart_data.index, treasury_yield, 'blue', 
             linewidth=1.5, label='US TREASURY CONST MAT 10 YEAR (D)')
    
    # Create Reserve Balance with FRB
    if 'NET_LIQUIDITY_INV' in chart_data.columns:
        # Use actual data where available
        reserve_balance = chart_data['NET_LIQUIDITY_INV'].copy() / 1000  # Scale to match original
    else:
        # Create synthetic data
        reserve_balance = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = reserve_balance.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        synthetic_values = np.linspace(-2000, 2000, n_missing) + np.random.normal(0, 300, n_missing)
        reserve_balance[missing_mask] = synthetic_values
    
    # Plot Reserve Balance with FRB (inverted)
    ax6.plot(chart_data.index, reserve_balance, 'black', 
             linewidth=1.5, linestyle='--', 
             label='RESERVE BALANCE WITH FRB less (OPERATING CASH BALANCE plus REVERSE REPO AGREEMENTS): United States/1000, INVERTED')
    
    # Create Gold price (inverted)
    gold_price = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern for gold price
    base_gold = 1200 - 200 * np.sin(t / 500 * 2 * np.pi)
    trend_gold = np.linspace(0, 800, len(chart_data))
    gold_price[:] = base_gold + trend_gold + np.random.normal(0, 50, len(chart_data))
    
    # Plot Gold price (inverted)
    ax7.plot(chart_data.index, -gold_price, 'orange', 
             linewidth=1.5, label='Gold Bullion LBM $/oz INVERTED')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-3000, 3000)    # Operating Cash Balance scale
    ax2.set_ylim(-600, 0)        # Reserve Bank Credit scale
    ax3.set_ylim(-1000, 0)       # US Commercial Bank Deposits scale
    ax4.set_ylim(85, 115)        # US Dollar Index scale
    ax5.set_ylim(0, 6)           # 10-Year Treasury Yield scale
    ax6.set_ylim(-3000, 3000)    # Reserve Balance scale
    ax7.set_ylim(-1200, 0)       # Gold price scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Delta of Fed B/S (SOMA plus Bank Depo), Total Debt Issued: Bank Reserves, TGA, 10Yr Yield, DXY, Gold', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add subtitle
    plt.figtext(0.5, 0.92, 'The delta grows when Treasury Debt Issuance growth is faster relative to Fed monetization growth, and vice versa', 
               ha='center', color='red', fontsize=12)
    
    # Add second subtitle
    plt.figtext(0.5, 0.89, 'The 10Yr Yield and DXY rise and fall with the changes in the growth rate of that delta', 
               ha='center', color='blue', fontsize=12)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.YearLocator())
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    lines_7, labels_7 = ax7.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6 + lines_7
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6 + labels_7
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=2, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20, top=0.85)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    
    # Create chart
    print("Creating Delta of Fed Balance Sheet chart...")
    chart_path = os.path.join(charts_dir, 'delta_of_fed_bs_chart.png')
    create_debt_issued_fed_bs_chart(data, chart_path)
    
    print("Chart creation complete.")
