#!/usr/bin/env python3
"""
Updated PDF Report Generation Script
This script creates a comprehensive PDF document with all the financial charts,
including the truly predictive correlation coefficient chart.
"""

import os
import base64
from fpdf import FPDF
import glob

# Create directory for the final report
report_dir = 'report'
os.makedirs(report_dir, exist_ok=True)

class ChartReport(FPDF):
    def __init__(self):
        super().__init__()
        self.WIDTH = 210
        self.HEIGHT = 297
        
    def header(self):
        # Set up the header
        self.set_font('Helvetica', 'B', 12)
        self.cell(0, 10, 'Financial Charts Analysis', 0, 1, 'C')
        self.ln(5)
        
    def footer(self):
        # Set up the footer
        self.set_y(-15)
        self.set_font('Helvetica', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')
        
    def chapter_title(self, title):
        # Add a chapter title
        self.set_font('Helvetica', 'B', 14)
        self.cell(0, 10, title, 0, 1, 'L')
        self.ln(5)
        
    def chapter_body(self, text):
        # Add chapter text
        self.set_font('Helvetica', '', 11)
        self.multi_cell(0, 6, text)
        self.ln(5)
        
    def add_image(self, image_path, w=180):
        # Calculate height to maintain aspect ratio
        img_width = w
        img_height = w * 0.5  # Assuming a 2:1 aspect ratio, adjust as needed
        
        # Center the image
        x = (self.WIDTH - img_width) / 2
        
        # Add the image
        self.image(image_path, x=x, y=None, w=img_width, h=img_height)
        self.ln(10)

def create_pdf_report(charts_dir='charts', output_file='report/financial_charts_report_final.pdf'):
    """Create a PDF report with all the charts, prioritizing the truly predictive correlation chart"""
    
    # Get all chart files
    chart_files = [f for f in os.listdir(charts_dir) if f.endswith('.png')]
    
    # Prioritize the truly predictive correlation chart
    if 'modified_correlation_coefficient_chart_truly_predictive.png' in chart_files:
        chart_files.remove('modified_correlation_coefficient_chart_truly_predictive.png')
        chart_files = ['modified_correlation_coefficient_chart_truly_predictive.png'] + sorted(chart_files)
    else:
        chart_files.sort()  # Sort to ensure consistent order
    
    # Create PDF
    pdf = ChartReport()
    pdf.add_page()
    
    # Add introduction
    pdf.chapter_title("Financial Charts Analysis")
    pdf.chapter_body(
        "This report presents a comprehensive analysis of various financial metrics and their relationships, "
        "recreated using Python and data from the Federal Reserve Economic Data (FRED) API. The charts illustrate "
        "correlations between market indices, debt issuance, Fed balance sheet components, and other key economic indicators."
    )
    
    # Chart descriptions
    chart_descriptions = {
        'modified_correlation_coefficient_chart_truly_predictive.png': {
            'title': "Modified Correlation Coefficient between S&P Comp Index vs VIX (Truly Predictive 4-week shift)",
            'description': 
                "This chart displays the Modified Correlation Coefficient (Mod CC) between the S&P 500 Composite Index and the VIX (CBOE Volatility Index) "
                "with a truly predictive 4-week shift. The correlation is calculated by comparing current VIX values with S&P 500 values 4 weeks in the future, "
                "showing how current volatility predicts future market performance.\n\n"
                "The black line represents the Modified Correlation Coefficient, which measures the relationship between market performance and volatility. "
                "The chart also includes the S&P 500 index (red), inverted VIX (green), normalized S&P 500 (blue), and a moving average of the Modified CC (cyan).\n\n"
                "The Modified CC is suggesting a top for SPX and bottom for VIX on October 16, indicating a potential market turning point where "
                "high stock prices coincide with low volatility - often a sign of market complacency before corrections."
        },
        
        'modified_correlation_coefficient_chart.png': {
            'title': "Modified Correlation Coefficient between S&P Comp Index vs VIX",
            'description': 
                "This chart displays the Modified Correlation Coefficient (Mod CC) between the S&P 500 Composite Index and the VIX (CBOE Volatility Index). "
                "The black line represents the Modified Correlation Coefficient, which measures the relationship between market performance and volatility. "
                "The chart also includes the S&P 500 index (red), inverted VIX (green), normalized S&P 500 (blue), and a moving average of the Modified CC (cyan)."
        },
        
        'gasoline_consumption_chart.png': {
            'title': "Gasoline Consumption, Production, and Related Metrics",
            'description': 
                "This chart illustrates the relationships between gasoline consumption, production, oil prices, gasoline prices, "
                "S&P 500, and 10-Year Treasury Yield. The multiple axes allow for comparison of these different metrics over time, "
                "revealing how energy consumption patterns correlate with broader economic indicators.\n\n"
                "The chart includes both actual data and forecasts for finished liquid fuels consumption, highlighting expected "
                "trends in energy markets. The highlighted area in the bottom right indicates a period of particular interest for analysis."
        },
        
        'fed_balance_sheet_chart.png': {
            'title': "Fed Balance Sheet (SOMA Components)",
            'description': 
                "This chart examines the Federal Reserve's balance sheet components, specifically the System Open Market Account (SOMA), "
                "along with the Treasury General Account, Overnight Reverse Repo operations, Net Liquidity, S&P 500, and 10-Year Treasury Yield.\n\n"
                "The Fed SOMA Components model suggests an S&P 500 top in the second week of April, indicating how central bank "
                "operations may influence market performance. The chart demonstrates the complex relationships between monetary policy "
                "operations and financial markets."
        },
        
        'debt_issuance_chart.png': {
            'title': "Debt Issuance, T-Bills, and Yields",
            'description': 
                "This chart displays the relationships between debt issuance, Treasury Bills issued, Overnight Reverse Repo operations, "
                "and 10-Year Treasury Yield. The visualization helps understand how government debt operations affect interest rates and "
                "market liquidity.\n\n"
                "The chart shows Reverse Repo Agreements (inverted), 10-Year Treasury Yield, Debt Held by the Public (logarithmic scale), "
                "changes in Treasury Bills, and Overnight Repo rates, providing insights into the mechanics of government financing."
        },
        
        'total_debt_vs_fed_bs_chart.png': {
            'title': "Total Debt Issued less Fed Balance Sheet",
            'description': 
                "This chart illustrates the relationship between total debt issued less the Federal Reserve's balance sheet (SOMA + Bank Deposits), "
                "Bank Reserves less Treasury General Account, Net Liquidity, and the VIX Index.\n\n"
                "The delta grows when Treasury Debt Issuance growth is faster relative to Fed monetization growth, and vice versa. "
                "The VIX Index is positively correlated to changes in net duration issuance (similar to the MOVE Index), demonstrating "
                "how market volatility responds to changes in debt dynamics."
        },
        
        'delta_of_fed_bs_chart.png': {
            'title': "Delta of Fed Balance Sheet",
            'description': 
                "This chart examines the delta of the Fed's balance sheet (SOMA plus Bank Deposits) in relation to Total Debt Issued, "
                "Bank Reserves, Treasury General Account, 10-Year Treasury Yield, US Dollar Index (DXY), and Gold.\n\n"
                "The delta grows when Treasury Debt Issuance growth is faster relative to Fed monetization growth, and vice versa. "
                "The 10-Year Yield and DXY rise and fall with the changes in the growth rate of that delta, illustrating how these key "
                "financial metrics respond to changes in the relationship between government debt and central bank operations."
        },
        
        'fed_aggregate_credit_chart.png': {
            'title': "Fed Aggregate Credit and Market Indicators",
            'description': 
                "This chart shows the relationships between Fed Aggregate Credit, S&P 500, VIX (Inverted), Bitcoin, Gold, and the "
                "US Dollar Index (Inverted) on a weekly basis.\n\n"
                "The visualization helps understand how the Federal Reserve's credit operations correlate with various market indicators, "
                "including traditional assets (stocks, gold), volatility measures, currency values, and alternative assets like Bitcoin."
        },
        
        'modified_correlation_coefficient_putcall_chart.png': {
            'title': "Modified Correlation Coefficient with Put/Call Ratio",
            'description': 
                "This chart expands on the Modified Correlation Coefficient between S&P 500 and VIX by adding the CBOE Put/Call Ratio. "
                "The Put/Call Ratio is an important sentiment indicator that measures the volume of put options relative to call options.\n\n"
                "The combination of the Modified Correlation Coefficient and Put/Call Ratio provides insights into market sentiment, "
                "potential turning points, and the relationship between market performance, volatility, and options market positioning."
        }
    }
    
    # Add each chart to the PDF
    for chart_file in chart_files:
        chart_path = os.path.join(charts_dir, chart_file)
        
        # Get description for this chart
        chart_info = chart_descriptions.get(chart_file, {
            'title': chart_file.replace('.png', '').replace('_', ' ').title(),
            'description': "This chart illustrates financial relationships and metrics derived from FRED data."
        })
        
        # Add a new page for each chart (except the first one)
        if chart_file != chart_files[0]:
            pdf.add_page()
        
        # Add chart title and description
        pdf.chapter_title(chart_info['title'])
        pdf.chapter_body(chart_info['description'])
        
        # Add the chart image
        pdf.add_image(chart_path)
    
    # Add conclusion
    pdf.add_page()
    pdf.chapter_title("Conclusion")
    pdf.chapter_body(
        "The charts presented in this report demonstrate the complex interrelationships between various financial metrics, "
        "including market indices, Fed operations, debt issuance, and economic indicators. These visualizations help in understanding "
        "how monetary policy, government financing, and market dynamics interact.\n\n"
        "Key observations include:\n"
        "- The Modified Correlation Coefficient provides insights into potential market turning points\n"
        "- Fed balance sheet operations show significant correlation with market performance\n"
        "- The relationship between Treasury debt issuance and Fed monetization affects various market metrics\n"
        "- Volatility indices (VIX and MOVE) respond to changes in debt dynamics\n"
        "- Energy consumption patterns correlate with broader economic indicators\n\n"
        "These charts were recreated using Python and data from the Federal Reserve Economic Data (FRED) API, "
        "demonstrating the power of data analysis in understanding financial markets."
    )
    
    # Save the PDF
    pdf.output(output_file)
    print(f"PDF report created: {output_file}")
    return output_file

if __name__ == "__main__":
    # Create PDF report
    create_pdf_report()
    
    print("Report compilation complete.")
