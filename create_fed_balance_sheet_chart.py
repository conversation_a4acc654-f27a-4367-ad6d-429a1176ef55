#!/usr/bin/env python3
"""
Chart Creation Script - Fed Balance Sheet Chart
This script creates the Fed Balance Sheet (SOMA Components) chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_fed_balance_sheet_chart(data, save_path):
    """
    Create the Fed Balance Sheet (SOMA Components) chart
    Similar to the third chart in the provided images
    """
    # Filter data to match the time range in the original chart (2022 - 2025)
    start_date = '2022-01-01'
    end_date = '2025-04-30'
    
    chart_data = data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Reserve Balance
    ax2 = ax1.twinx()           # Right axis for Fed Aggregate Credit
    ax3 = ax1.twinx()           # Far right axis for 10Yr Yield (inverted)
    ax4 = ax1.twinx()           # Additional axis for S&P 500
    ax5 = ax1.twinx()           # Additional axis for Securities Held
    ax6 = ax1.twinx()           # Additional axis for Net Liquidity
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    
    # Plot Reserve Balance with FRB
    if 'WRESBAL' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['WRESBAL_1000'] = chart_data['WRESBAL'] / 1000
        ax1.plot(chart_data.index, chart_data['WRESBAL_1000'], 'k-', 
                 linewidth=1.5, label='RESERVE BALANCE WITH FRB: United States/1000')
    
    # Plot Fed Aggregate Credit
    if 'WALCL' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['WALCL_1000'] = chart_data['WALCL'] / 1000
        ax2.plot(chart_data.index, chart_data['WALCL_1000'], 'g-', 
                 linewidth=1.5, label='FED AGGREGATE CREDIT: United States/1000')
    
    # Plot Normalized & Inverted 10-Year Treasury Yield
    if 'DGS10_NORM_INV' in chart_data.columns:
        ax3.plot(chart_data.index, chart_data['DGS10_NORM_INV'], 'm-', 
                 linewidth=1.5, label='NORMALIZED - US TREASURY CONST MAT 10 YEAR (INVERTED)')
    
    # Plot S&P 500
    if 'SP500' in chart_data.columns:
        ax4.plot(chart_data.index, chart_data['SP500'], 'b-', 
                 linewidth=1.5, label='S&P 500 COMPOSITE')
    
    # Create a proxy for Securities Held Outright
    if 'WALCL' in chart_data.columns and 'WRESBAL' in chart_data.columns and 'RRPONTSYD' in chart_data.columns:
        # Create a proxy based on available data
        chart_data['SECS_HELD_PROXY'] = (chart_data['WALCL'] - chart_data['WRESBAL'] - chart_data['RRPONTSYD']) / 1000
        ax5.plot(chart_data.index, chart_data['SECS_HELD_PROXY'], color='darkgreen', 
                 linewidth=1.5, label='SECS HELD OUTRIGHT NTS & BDS NOMINL less (OPERATING CASH BALANCE + REVERSE REPO AGREEMENTS): United States/1000/1000')
    
    # Create a proxy for Net Liquidity
    if 'NET_LIQUIDITY_1000' in chart_data.columns:
        ax6.plot(chart_data.index, chart_data['NET_LIQUIDITY_1000'], color='lightblue', 
                 linewidth=1.5, label='NET LIQUIDITY: RESERVE BALANCE WITH FRB plus (OPERATING CASH BALANCE plus REVERSE REPO AGREEMENTS): United States/1000/1000')
    
    # Create a proxy for TGA Operating Cash Balance (Inverted)
    if 'TGA_INV' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['TGA_INV_1000'] = chart_data['TGA_INV'] / 1000
        ax1.plot(chart_data.index, chart_data['TGA_INV_1000'], 'k--', 
                 linewidth=1.5, label='TGA_OPERATING CASH BALANCE: United States/1000, INVERTED')
    
    # Create a proxy for Factors Supplying Reserve Funds
    if 'WALCL' in chart_data.columns and 'WRESBAL' in chart_data.columns:
        # Create a proxy based on available data
        chart_data['FACTORS_PROXY'] = (chart_data['WALCL'] - chart_data['WRESBAL']) / 1000
        ax2.plot(chart_data.index, chart_data['FACTORS_PROXY'], color='lime', 
                 linewidth=1.5, label='FACTORS SUPPLYING RESERVE FUNDS less FACTORS ABSORBING RESERVE FUNDS: United States/1000')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-1500, 1000)    # Reserve Balance scale
    ax2.set_ylim(-50, 200)       # Fed Aggregate Credit scale
    ax3.set_ylim(-1.5, 1.0)      # 10-Year yield scale (inverted)
    ax4.set_ylim(3500, 6500)     # S&P 500 scale
    ax5.set_ylim(-50, 200)       # Securities Held scale
    ax6.set_ylim(-1500, 1000)    # Net Liquidity scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Fed Balance Sheet (SOMA Components), Treas Gen\'l Acct, O/N RRP, Net Liquidity, SPX, 10Yr Yield', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add subtitle
    plt.figtext(0.5, 0.92, 'Fed SOMA Components model suggests an SPX 2nd week April top', 
               ha='center', color='blue', fontsize=14)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.YearLocator())
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=2, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    
    # Create chart
    print("Creating Fed Balance Sheet chart...")
    chart_path = os.path.join(charts_dir, 'fed_balance_sheet_chart.png')
    create_fed_balance_sheet_chart(data, chart_path)
    
    print("Chart creation complete.")
