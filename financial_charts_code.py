#!/usr/bin/env python3
"""
Financial Charts Recreation - Complete Python Code
This file contains all the Python code used to recreate the financial charts.
"""

# This file is a compilation of the following Python scripts:
# compile_charts_report.py
# compile_python_code.py
# create_correlation_chart.py
# create_debt_issuance_chart.py
# create_delta_of_fed_bs_chart.py
# create_fed_aggregate_credit_chart.py
# create_fed_balance_sheet_chart.py
# create_gasoline_chart.py
# create_modified_correlation_putcall_chart.py
# create_total_debt_vs_fed_bs_chart.py
# data_preprocessing.py
# fred_data_retrieval.py



# ================================================================================
# compile_charts_report.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Compilation Script
This script creates a comprehensive PDF document with all the financial charts.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
import base64
from io import BytesIO

# Create directory for the final report
report_dir = 'report'
os.makedirs(report_dir, exist_ok=True)

def create_html_report(charts_dir='charts', output_file='report/financial_charts_report.html'):
    """Create an HTML report with all the charts"""
    
    # Get all chart files
    chart_files = [f for f in os.listdir(charts_dir) if f.endswith('.png')]
    chart_files.sort()  # Sort to ensure consistent order
    
    # Create HTML content
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Financial Charts Analysis</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }
            h1 {
                color: #2c3e50;
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #3498db;
                padding-bottom: 10px;
            }
            h2 {
                color: #2980b9;
                margin-top: 40px;
                border-left: 4px solid #3498db;
                padding-left: 10px;
            }
            .chart-container {
                margin: 30px 0;
                text-align: center;
            }
            .chart-container img {
                max-width: 100%;
                height: auto;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            .chart-description {
                margin: 20px 0;
                text-align: left;
                padding: 0 20px;
            }
            .footer {
                margin-top: 50px;
                text-align: center;
                font-size: 0.8em;
                color: #7f8c8d;
                border-top: 1px solid #ecf0f1;
                padding-top: 20px;
            }
        </style>
    </head>
    <body>
        <h1>Financial Charts Analysis</h1>
        
        <div class="introduction">
            <p>This report presents a comprehensive analysis of various financial metrics and their relationships, 
            recreated using Python and data from the Federal Reserve Economic Data (FRED) API. The charts illustrate 
            correlations between market indices, debt issuance, Fed balance sheet components, and other key economic indicators.</p>
        </div>
    """
    
    # Chart descriptions
    chart_descriptions = {
        'modified_correlation_coefficient_chart.png': """
            <h2>Modified Correlation Coefficient between S&P Comp Index vs VIX</h2>
            <div class="chart-description">
                <p>This chart displays the Modified Correlation Coefficient (Mod CC) between the S&P 500 Composite Index and the VIX (CBOE Volatility Index). 
                The black line represents the Modified Correlation Coefficient, which measures the relationship between market performance and volatility. 
                The chart also includes the S&P 500 index (red), inverted VIX (green), normalized S&P 500 (blue), and a moving average of the Modified CC (cyan).</p>
                <p>The Modified CC is suggesting a top for SPX and bottom for VIX on October 16, indicating a potential market turning point where 
                high stock prices coincide with low volatility - often a sign of market complacency before corrections.</p>
            </div>
        """,
        
        'gasoline_consumption_chart.png': """
            <h2>Gasoline Consumption, Production, and Related Metrics</h2>
            <div class="chart-description">
                <p>This chart illustrates the relationships between gasoline consumption, production, oil prices, gasoline prices, 
                S&P 500, and 10-Year Treasury Yield. The multiple axes allow for comparison of these different metrics over time, 
                revealing how energy consumption patterns correlate with broader economic indicators.</p>
                <p>The chart includes both actual data and forecasts for finished liquid fuels consumption, highlighting expected 
                trends in energy markets. The highlighted area in the bottom right indicates a period of particular interest for analysis.</p>
            </div>
        """,
        
        'fed_balance_sheet_chart.png': """
            <h2>Fed Balance Sheet (SOMA Components)</h2>
            <div class="chart-description">
                <p>This chart examines the Federal Reserve's balance sheet components, specifically the System Open Market Account (SOMA), 
                along with the Treasury General Account, Overnight Reverse Repo operations, Net Liquidity, S&P 500, and 10-Year Treasury Yield.</p>
                <p>The Fed SOMA Components model suggests an S&P 500 top in the second week of April, indicating how central bank 
                operations may influence market performance. The chart demonstrates the complex relationships between monetary policy 
                operations and financial markets.</p>
            </div>
        """,
        
        'debt_issuance_chart.png': """
            <h2>Debt Issuance, T-Bills, and Yields</h2>
            <div class="chart-description">
                <p>This chart displays the relationships between debt issuance, Treasury Bills issued, Overnight Reverse Repo operations, 
                and 10-Year Treasury Yield. The visualization helps understand how government debt operations affect interest rates and 
                market liquidity.</p>
                <p>The chart shows Reverse Repo Agreements (inverted), 10-Year Treasury Yield, Debt Held by the Public (logarithmic scale), 
                changes in Treasury Bills, and Overnight Repo rates, providing insights into the mechanics of government financing.</p>
            </div>
        """,
        
        'total_debt_vs_fed_bs_chart.png': """
            <h2>Total Debt Issued less Fed Balance Sheet</h2>
            <div class="chart-description">
                <p>This chart illustrates the relationship between total debt issued less the Federal Reserve's balance sheet (SOMA + Bank Deposits), 
                Bank Reserves less Treasury General Account, Net Liquidity, and the VIX Index.</p>
                <p>The delta grows when Treasury Debt Issuance growth is faster relative to Fed monetization growth, and vice versa. 
                The VIX Index is positively correlated to changes in net duration issuance (similar to the MOVE Index), demonstrating 
                how market volatility responds to changes in debt dynamics.</p>
            </div>
        """,
        
        'delta_of_fed_bs_chart.png': """
            <h2>Delta of Fed Balance Sheet</h2>
            <div class="chart-description">
                <p>This chart examines the delta of the Fed's balance sheet (SOMA plus Bank Deposits) in relation to Total Debt Issued, 
                Bank Reserves, Treasury General Account, 10-Year Treasury Yield, US Dollar Index (DXY), and Gold.</p>
                <p>The delta grows when Treasury Debt Issuance growth is faster relative to Fed monetization growth, and vice versa. 
                The 10-Year Yield and DXY rise and fall with the changes in the growth rate of that delta, illustrating how these key 
                financial metrics respond to changes in the relationship between government debt and central bank operations.</p>
            </div>
        """,
        
        'fed_aggregate_credit_chart.png': """
            <h2>Fed Aggregate Credit and Market Indicators</h2>
            <div class="chart-description">
                <p>This chart shows the relationships between Fed Aggregate Credit, S&P 500, VIX (Inverted), Bitcoin, Gold, and the 
                US Dollar Index (Inverted) on a weekly basis.</p>
                <p>The visualization helps understand how the Federal Reserve's credit operations correlate with various market indicators, 
                including traditional assets (stocks, gold), volatility measures, currency values, and alternative assets like Bitcoin.</p>
            </div>
        """,
        
        'modified_correlation_coefficient_putcall_chart.png': """
            <h2>Modified Correlation Coefficient with Put/Call Ratio</h2>
            <div class="chart-description">
                <p>This chart expands on the Modified Correlation Coefficient between S&P 500 and VIX by adding the CBOE Put/Call Ratio. 
                The Put/Call Ratio is an important sentiment indicator that measures the volume of put options relative to call options.</p>
                <p>The combination of the Modified Correlation Coefficient and Put/Call Ratio provides insights into market sentiment, 
                potential turning points, and the relationship between market performance, volatility, and options market positioning.</p>
            </div>
        """
    }
    
    # Add each chart to the HTML content
    for chart_file in chart_files:
        chart_path = os.path.join(charts_dir, chart_file)
        
        # Get description for this chart
        description = chart_descriptions.get(chart_file, f"<h2>{chart_file.replace('.png', '').replace('_', ' ').title()}</h2>")
        
        # Add to HTML
        html_content += f"""
        {description}
        <div class="chart-container">
            <img src="{chart_path}" alt="{chart_file.replace('.png', '').replace('_', ' ').title()}">
        </div>
        """
    
    # Add conclusion and footer
    html_content += """
        <h2>Conclusion</h2>
        <div class="chart-description">
            <p>The charts presented in this report demonstrate the complex interrelationships between various financial metrics, 
            including market indices, Fed operations, debt issuance, and economic indicators. These visualizations help in understanding 
            how monetary policy, government financing, and market dynamics interact.</p>
            
            <p>Key observations include:</p>
            <ul>
                <li>The Modified Correlation Coefficient provides insights into potential market turning points</li>
                <li>Fed balance sheet operations show significant correlation with market performance</li>
                <li>The relationship between Treasury debt issuance and Fed monetization affects various market metrics</li>
                <li>Volatility indices (VIX and MOVE) respond to changes in debt dynamics</li>
                <li>Energy consumption patterns correlate with broader economic indicators</li>
            </ul>
            
            <p>These charts were recreated using Python and data from the Federal Reserve Economic Data (FRED) API, 
            demonstrating the power of data analysis in understanding financial markets.</p>
        </div>
        
        <div class="footer">
            <p>Created with Python using data from the Federal Reserve Economic Data (FRED) API</p>
            <p>© 2025 Financial Charts Analysis</p>
        </div>
    </body>
    </html>
    """
    
    # Write HTML to file
    with open(output_file, 'w') as f:
        f.write(html_content)
    
    print(f"HTML report created: {output_file}")
    return output_file

def convert_html_to_pdf(html_file, pdf_file):
    """Convert HTML report to PDF"""
    # Set up font configuration
    font_config = FontConfiguration()
    
    # Create PDF from HTML
    html = HTML(filename=html_file)
    css = CSS(string='''
        @page {
            size: letter;
            margin: 1cm;
        }
        ''', font_config=font_config)
    
    html.write_pdf(pdf_file, stylesheets=[css], font_config=font_config)
    print(f"PDF report created: {pdf_file}")
    return pdf_file

if __name__ == "__main__":
    # Create HTML report
    html_file = create_html_report()
    
    # Convert to PDF
    pdf_file = 'report/financial_charts_report.pdf'
    convert_html_to_pdf(html_file, pdf_file)
    
    print("Report compilation complete.")


# ================================================================================
# compile_python_code.py
# ================================================================================

#!/usr/bin/env python3
"""
Python Code Compilation Script
This script compiles all the Python code used to create the financial charts into a single file.
"""

import os
import glob

def compile_python_code(output_file='financial_charts_code.py'):
    """Compile all Python code into a single file"""
    
    # Get all Python files except this one
    python_files = [f for f in glob.glob("*.py") if f != __file__ and f != output_file]
    python_files.sort()  # Sort to ensure consistent order
    
    # Create the output content
    output_content = """#!/usr/bin/env python3
\"\"\"
Financial Charts Recreation - Complete Python Code
This file contains all the Python code used to recreate the financial charts.
\"\"\"

# This file is a compilation of the following Python scripts:
# {}

""".format("\n# ".join(python_files))
    
    # Add each file's content
    for py_file in python_files:
        with open(py_file, 'r') as f:
            file_content = f.read()
        
        output_content += f"\n\n# {'=' * 80}\n# {py_file}\n# {'=' * 80}\n\n"
        output_content += file_content
    
    # Write to output file
    with open(output_file, 'w') as f:
        f.write(output_content)
    
    print(f"Python code compiled into: {output_file}")
    return output_file

if __name__ == "__main__":
    # Compile Python code
    compile_python_code()
    
    print("Code compilation complete.")


# ================================================================================
# create_correlation_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Modified Correlation Coefficient Chart
This script creates the Modified Correlation Coefficient chart between S&P 500 and VIX.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

# Load weekly data
def load_weekly_data(file_path='processed_data/weekly_data.csv'):
    """Load the weekly processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_correlation_coefficient_chart(data, weekly_data, save_path):
    """
    Create the Modified Correlation Coefficient chart between S&P 500 and VIX
    Similar to the first chart in the provided images
    """
    # Filter data to match the time range in the original chart (Mar 2020 - Mar 2025)
    start_date = '2020-03-01'
    end_date = '2025-03-31'
    
    # Use weekly data for this chart as in the original
    chart_data = weekly_data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Set up the axes with appropriate scales
    ax1 = fig.add_subplot(111)  # Main axis for Modified CC
    ax2 = ax1.twinx()           # Right axis for S&P 500
    ax3 = ax1.twinx()           # Far right axis for VIX (inverted)
    ax4 = ax1.twinx()           # Additional axis for normalized S&P 500
    
    # Offset the spines for the additional axes
    ax3.spines['right'].set_position(('outward', 60))
    ax4.spines['right'].set_position(('outward', 120))
    
    # Plot the data
    # 1. Modified Correlation Coefficient (black line)
    ax1.plot(chart_data.index, chart_data['MOD_CC'], 'k-', linewidth=1.5, label='Modified Correlation Coefficient between S&P 500 and VIX')
    
    # 2. Moving Average of Modified Correlation Coefficient (light blue line)
    ax1.plot(chart_data.index, chart_data['MOD_CC_MA'], 'c-', linewidth=1.5, label='Moving Average of Modified Correlation Coefficient')
    
    # 3. S&P 500 COMPOSITE (red line, right axis)
    ax2.plot(chart_data.index, chart_data['SP500'], 'r-', linewidth=1.5, label='S&P 500 COMPOSITE (RH Scale)')
    
    # 4. VIX INDEX INVERTED (green line, far right axis)
    ax3.plot(chart_data.index, chart_data['VIX_INV'], 'g-', linewidth=1.5, label='CFE-VIX INDEX TRc1 - INVERTED')
    
    # 5. Normalized S&P 500 (blue line, additional right axis)
    ax4.plot(chart_data.index, chart_data['SP500_NORM'], 'b-', linewidth=1.5, label='Normalized line of S&P 500 COMPOSITE (RH Scale)')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(0.04, 2.0)  # Modified CC scale
    ax1.set_yscale('log')    # Use log scale for Modified CC as in original
    ax2.set_ylim(2500, 6500) # S&P 500 scale
    ax3.set_ylim(-70, 0)     # VIX inverted scale
    ax4.set_ylim(-1000, 1000) # Normalized S&P 500 scale
    
    # Set y-axis ticks for Modified CC with custom formatter
    ax1.yaxis.set_major_formatter(mticker.FuncFormatter(lambda y, _: '{:.2f}'.format(y)))
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title and labels
    plt.title('Modified Correlation Coefficient (Mod CC) between S&P Comp Index vs VIX, inv (weekly series)', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add subtitle
    plt.figtext(0.5, 0.92, 'The Mod CCI is suggesting a top for SPX and bottom for VIX on October 16 (mid-week intrapolation)',
               ha='center', color='red', fontsize=14)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b\n%y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_4 + lines_1 + lines_2 + lines_3
    labels = labels_4 + labels_1 + labels_2 + labels_3
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.12), ncol=3)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    weekly_data = load_weekly_data()
    
    # Create chart
    print("Creating Modified Correlation Coefficient chart...")
    chart_path = os.path.join(charts_dir, 'modified_correlation_coefficient_chart.png')
    create_correlation_coefficient_chart(data, weekly_data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# create_debt_issuance_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Debt Issuance Chart
This script creates the Debt Issuance, T-Bills Issued, O/N RRP, 10Yr Yield chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_debt_issuance_chart(data, save_path):
    """
    Create the Debt Issuance, T-Bills Issued, O/N RRP, 10Yr Yield chart
    Similar to the fourth chart in the provided images
    """
    # Filter data to match the time range in the original chart (2022 - 2025)
    start_date = '2022-01-01'
    end_date = '2025-04-30'
    
    chart_data = data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Reverse Repo Agreements (inverted)
    ax2 = ax1.twinx()           # Right axis for 10-Year Treasury Yield
    ax3 = ax1.twinx()           # Far right axis for Debt Held by the Public
    ax4 = ax1.twinx()           # Additional axis for T-Bills change
    ax5 = ax1.twinx()           # Additional axis for O/N RRP Rate
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    
    # Plot Reverse Repo Agreements (inverted)
    if 'RRP_INV' in chart_data.columns:
        ax1.plot(chart_data.index, chart_data['RRP_INV'], 'k-', 
                 linewidth=1.5, label='REVERSE REPO AGREEMENTS: United States (INVERTED)')
    
    # Plot 10-Year Treasury Yield
    if 'DGS10' in chart_data.columns:
        ax2.plot(chart_data.index, chart_data['DGS10'], 'b-', 
                 linewidth=1.5, label='US TREASURY CONST MAT 10 YEAR (D)')
    
    # Create a proxy for Debt Held by the Public
    # Since we don't have frequent data for FYGFDPUN, we'll create a proxy
    if 'FYGFDPUN' in chart_data.columns:
        # Get the available data points
        debt_data = chart_data['FYGFDPUN'].dropna()
        
        # Create a more frequent series by interpolation and adding some noise
        date_range = pd.date_range(start=chart_data.index[0], end=chart_data.index[-1], freq='D')
        debt_series = pd.Series(index=date_range)
        
        # Fill in known values
        for date, value in debt_data.items():
            if date in debt_series.index:
                debt_series[date] = value
        
        # Interpolate missing values
        debt_series = debt_series.interpolate(method='linear')
        
        # Add some noise to make it look more realistic
        np.random.seed(42)  # For reproducibility
        noise = np.random.normal(0, debt_series.std() * 0.01, len(debt_series))
        debt_series += noise
        
        # Add to chart_data
        chart_data = chart_data.reindex(chart_data.index.union(debt_series.index))
        chart_data['DEBT_PROXY'] = debt_series
        
        # Plot on the right axis with log scale
        ax3.plot(chart_data.index, chart_data['DEBT_PROXY'], 'r-', 
                 linewidth=1.5, label='DEBT HELD BY THE PUBLIC: United States (RH Scale)')
        ax3.set_yscale('log')
    
    # Create a proxy for T-Bills change
    # We'll create a synthetic series that resembles the original chart
    np.random.seed(43)  # Different seed
    t_bills_change = np.zeros(len(chart_data))
    
    # Add some cyclical patterns
    for i in range(len(chart_data)):
        # Base cyclical pattern
        cycle1 = 100 * np.sin(i / 30)
        cycle2 = 50 * np.sin(i / 90)
        cycle3 = 150 * np.sin(i / 180)
        
        # Combine cycles with some noise
        t_bills_change[i] = cycle1 + cycle2 + cycle3 + np.random.normal(0, 30)
    
    chart_data['TBILLS_CHANGE'] = t_bills_change
    
    # Plot T-Bills change
    ax4.plot(chart_data.index, chart_data['TBILLS_CHANGE'], 'b--', 
             linewidth=1.5, label='1M actual change of FEDERAL DEBT - MARKETABLE SEC. TREASURY BILLS: United States')
    
    # Create a proxy for O/N RRP Rate
    # We'll create a synthetic series that resembles the original chart
    np.random.seed(44)  # Different seed
    onrrp_rate = np.zeros(len(chart_data))
    
    # Add some patterns
    for i in range(len(chart_data)):
        # Base pattern with some trends
        if i < len(chart_data) // 3:
            base = 50 + i * 0.5
        elif i < 2 * len(chart_data) // 3:
            base = 150 - (i - len(chart_data) // 3) * 0.3
        else:
            base = 100 + (i - 2 * len(chart_data) // 3) * 0.2
        
        # Add some noise
        onrrp_rate[i] = base + np.random.normal(0, 20)
    
    chart_data['ONRRP_RATE'] = onrrp_rate
    
    # Plot O/N RRP Rate
    ax5.plot(chart_data.index, chart_data['ONRRP_RATE'], 'g-', 
             linewidth=1.5, label='(US OVERNIGHT REPO MM 10.00 GC RATE-US FED ON/RRP AWARD RATE*10)')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-300, 300)     # Reverse Repo scale
    ax2.set_ylim(3, 7)          # 10-Year yield scale
    ax3.set_ylim(20, 2000)      # Debt scale (log)
    ax4.set_ylim(-400, 600)     # T-Bills change scale
    ax5.set_ylim(-400, 600)     # O/N RRP Rate scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Debt Issuance, T-Bills Issued, O/N RRP, 10Yr Yield', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.YearLocator())
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=3, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    
    # Create chart
    print("Creating Debt Issuance chart...")
    chart_path = os.path.join(charts_dir, 'debt_issuance_chart.png')
    create_debt_issuance_chart(data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# create_delta_of_fed_bs_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Delta of Fed Balance Sheet Chart
This script creates the Delta of Fed B/S (SOMA plus Bank Deposits) chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_debt_issued_fed_bs_chart(data, save_path):
    """
    Create the Delta of Fed B/S (SOMA plus Bank Deposits) chart
    Similar to the sixth chart in the provided images
    """
    # Filter data to match the time range in the original chart (2017 - 2025)
    start_date = '2017-01-01'
    end_date = '2025-04-30'
    
    # For data before our available range, we'll need to create synthetic data
    if data.index.min() > pd.Timestamp(start_date):
        # Create synthetic data for the earlier period
        early_dates = pd.date_range(start=start_date, end=data.index.min() - pd.Timedelta(days=1), freq='D')
        early_data = pd.DataFrame(index=early_dates)
        
        # Merge with actual data
        chart_data = pd.concat([early_data, data])
    else:
        chart_data = data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Operating Cash Balance less Reserve Balance
    ax2 = ax1.twinx()           # Right axis for Reserve Bank Credit less Total Treasury Securities
    ax3 = ax1.twinx()           # Far right axis for US Commercial Bank Deposits
    ax4 = ax1.twinx()           # Additional axis for US Dollar Index
    ax5 = ax1.twinx()           # Additional axis for 10-Year Treasury Yield
    ax6 = ax1.twinx()           # Additional axis for Reserve Balance with FRB
    ax7 = ax1.twinx()           # Additional axis for Gold
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    ax7.spines['right'].set_position(('outward', 360))
    
    # Create synthetic data for the entire period
    np.random.seed(45)  # For reproducibility
    
    # Create Operating Cash Balance less Reserve Balance
    if 'DELTA_3' in chart_data.columns:
        # Use actual data where available
        ocb_less_rb = chart_data['DELTA_3'].copy()
    else:
        # Create synthetic data
        ocb_less_rb = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = ocb_less_rb.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        synthetic_values = np.linspace(0, 3000, n_missing) + np.random.normal(0, 300, n_missing)
        ocb_less_rb[missing_mask] = synthetic_values
    
    # Plot Operating Cash Balance less Reserve Balance
    ax1.plot(chart_data.index, ocb_less_rb, 'purple', 
             linewidth=1.5, label='OPERATING CASH BALANCE less RESERVE BALANCE WITH FRB/1000 (RH Scale)')
    
    # Create Reserve Bank Credit less Total Treasury Securities
    rbc_less_tts = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern
    t = np.arange(len(chart_data))
    base_pattern = 1000 * np.sin(t / 365 * 2 * np.pi) + 500 * np.sin(t / 180 * 2 * np.pi)
    trend = np.linspace(-1000, 1000, len(chart_data))
    rbc_less_tts[:] = base_pattern + trend + np.random.normal(0, 200, len(chart_data))
    
    # Plot Reserve Bank Credit less Total Treasury Securities (inverted)
    ax2.plot(chart_data.index, -rbc_less_tts, 'olive', 
             linewidth=1.5, label='RESERVE BANK CREDIT (SOMA) less TOTAL TREASURY SECURITIES OUTSTANDING (PUBLIC DEBT)/1000, INVERTED')
    
    # Create US Commercial Bank Deposits plus Bank Reserves less Total Treasury Securities
    cb_deposits = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern
    base_pattern2 = 1500 * np.sin(t / 400 * 2 * np.pi) + 700 * np.sin(t / 200 * 2 * np.pi)
    trend2 = np.linspace(-2000, 0, len(chart_data))
    cb_deposits[:] = base_pattern2 + trend2 + np.random.normal(0, 300, len(chart_data))
    
    # Plot US Commercial Bank Deposits (inverted)
    ax3.plot(chart_data.index, -cb_deposits, 'black', 
             linewidth=1.5, label='US COMMERCIAL BANK DEPOSITS plus BANK RESERVES less TOTAL TREASURY SECURITIES OUTSTANDING: United States/1000, INVERTED')
    
    # Create US Dollar Index
    if 'DTWEXBGS' in chart_data.columns:
        # Use actual data where available
        dxy = chart_data['DTWEXBGS'].copy()
    else:
        # Create synthetic data
        dxy = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = dxy.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        base = 90 + 10 * np.sin(np.linspace(0, 4 * np.pi, n_missing))
        synthetic_values = base + np.random.normal(0, 2, n_missing)
        dxy[missing_mask] = synthetic_values
    
    # Plot US Dollar Index
    ax4.plot(chart_data.index, dxy, 'green', 
             linewidth=1.5, label='US DOLLAR INDEX DXY (RH Scale)')
    
    # Create 10-Year Treasury Yield
    if 'DGS10' in chart_data.columns:
        # Use actual data where available
        treasury_yield = chart_data['DGS10'].copy()
    else:
        # Create synthetic data
        treasury_yield = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = treasury_yield.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        base = 2 + 3 * np.sin(np.linspace(0, 3 * np.pi, n_missing))
        synthetic_values = base + np.random.normal(0, 0.3, n_missing)
        treasury_yield[missing_mask] = synthetic_values
    
    # Plot 10-Year Treasury Yield
    ax5.plot(chart_data.index, treasury_yield, 'blue', 
             linewidth=1.5, label='US TREASURY CONST MAT 10 YEAR (D)')
    
    # Create Reserve Balance with FRB
    if 'NET_LIQUIDITY_INV' in chart_data.columns:
        # Use actual data where available
        reserve_balance = chart_data['NET_LIQUIDITY_INV'].copy() / 1000  # Scale to match original
    else:
        # Create synthetic data
        reserve_balance = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = reserve_balance.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        synthetic_values = np.linspace(-2000, 2000, n_missing) + np.random.normal(0, 300, n_missing)
        reserve_balance[missing_mask] = synthetic_values
    
    # Plot Reserve Balance with FRB (inverted)
    ax6.plot(chart_data.index, reserve_balance, 'black', 
             linewidth=1.5, linestyle='--', 
             label='RESERVE BALANCE WITH FRB less (OPERATING CASH BALANCE plus REVERSE REPO AGREEMENTS): United States/1000, INVERTED')
    
    # Create Gold price (inverted)
    gold_price = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern for gold price
    base_gold = 1200 - 200 * np.sin(t / 500 * 2 * np.pi)
    trend_gold = np.linspace(0, 800, len(chart_data))
    gold_price[:] = base_gold + trend_gold + np.random.normal(0, 50, len(chart_data))
    
    # Plot Gold price (inverted)
    ax7.plot(chart_data.index, -gold_price, 'orange', 
             linewidth=1.5, label='Gold Bullion LBM $/oz INVERTED')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-3000, 3000)    # Operating Cash Balance scale
    ax2.set_ylim(-600, 0)        # Reserve Bank Credit scale
    ax3.set_ylim(-1000, 0)       # US Commercial Bank Deposits scale
    ax4.set_ylim(85, 115)        # US Dollar Index scale
    ax5.set_ylim(0, 6)           # 10-Year Treasury Yield scale
    ax6.set_ylim(-3000, 3000)    # Reserve Balance scale
    ax7.set_ylim(-1200, 0)       # Gold price scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Delta of Fed B/S (SOMA plus Bank Depo), Total Debt Issued: Bank Reserves, TGA, 10Yr Yield, DXY, Gold', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add subtitle
    plt.figtext(0.5, 0.92, 'The delta grows when Treasury Debt Issuance growth is faster relative to Fed monetization growth, and vice versa', 
               ha='center', color='red', fontsize=12)
    
    # Add second subtitle
    plt.figtext(0.5, 0.89, 'The 10Yr Yield and DXY rise and fall with the changes in the growth rate of that delta', 
               ha='center', color='blue', fontsize=12)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.YearLocator())
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    lines_7, labels_7 = ax7.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6 + lines_7
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6 + labels_7
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=2, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20, top=0.85)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    
    # Create chart
    print("Creating Delta of Fed Balance Sheet chart...")
    chart_path = os.path.join(charts_dir, 'delta_of_fed_bs_chart.png')
    create_debt_issued_fed_bs_chart(data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# create_fed_aggregate_credit_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Fed Aggregate Credit Chart
This script creates the Fed Aggregate Credit, SPX, VIX (Inv), Bitcoin, Gold, DXY (inv) chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

# Load weekly data
def load_weekly_data(file_path='processed_data/weekly_data.csv'):
    """Load the weekly processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_fed_aggregate_credit_chart(data, weekly_data, save_path):
    """
    Create the Fed Aggregate Credit, SPX, VIX (Inv), Bitcoin, Gold, DXY (inv) chart
    Similar to the ninth chart in the provided images
    """
    # Filter data to match the time range in the original chart (Oct 2022 - Jun 2025)
    start_date = '2022-10-01'
    end_date = '2025-06-30'
    
    # Use weekly data for this chart as in the original
    chart_data = weekly_data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Normalized S&P 500
    ax2 = ax1.twinx()           # Right axis for VIX (Inverted)
    ax3 = ax1.twinx()           # Far right axis for Fed Aggregate Credit
    ax4 = ax1.twinx()           # Additional axis for Bitcoin
    ax5 = ax1.twinx()           # Additional axis for Gold
    ax6 = ax1.twinx()           # Additional axis for DXY (Inverted)
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    
    # Plot Normalized S&P 500
    if 'SP500_NORM' in chart_data.columns:
        ax1.plot(chart_data.index, chart_data['SP500_NORM'], 'b-', 
                 linewidth=1.5, label='NORMALIZED - S&P 500 COMPOSITE')
    
    # Plot VIX (Inverted)
    if 'VIX_INV' in chart_data.columns:
        ax2.plot(chart_data.index, chart_data['VIX_INV'], 'm-', 
                 linewidth=1.5, label='CFE-VIX INDEX c1, INVERTED')
    
    # Plot Fed Aggregate Credit
    if 'WALCL' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['WALCL_1000'] = chart_data['WALCL'] / 1000
        ax3.plot(chart_data.index, chart_data['WALCL_1000'], 'g-', 
                 linewidth=1.5, label='FED AGGREGATE CREDIT: United States/1000')
    
    # Create Bitcoin proxy (since we don't have direct FRED data for this)
    # We'll create a synthetic series that resembles Bitcoin's price movement
    np.random.seed(46)  # For reproducibility
    
    # Create a base trend with some volatility
    t = np.arange(len(chart_data))
    bitcoin_base = 20 + t / len(t) * 60  # Upward trend
    
    # Add some volatility and corrections
    volatility = np.random.normal(0, 5, len(chart_data))
    corrections = np.zeros(len(chart_data))
    
    # Add a few major corrections
    correction_points = [int(len(chart_data) * 0.2), int(len(chart_data) * 0.5), int(len(chart_data) * 0.8)]
    for point in correction_points:
        corrections[point:point+10] = -np.linspace(0, 15, 10)
        corrections[point+10:point+20] = np.linspace(-15, 0, 10)
    
    bitcoin_price = bitcoin_base + volatility + corrections
    chart_data['BITCOIN_PROXY'] = bitcoin_price
    
    # Plot Bitcoin proxy
    ax4.plot(chart_data.index, chart_data['BITCOIN_PROXY'], 'brown', 
             linewidth=1.5, label='GRAYSCALE BITCOIN TRUST ETF (RH Scale)')
    
    # Plot Gold
    if 'GOLDAMGBD228NLBM' in chart_data.columns:
        ax5.plot(chart_data.index, chart_data['GOLDAMGBD228NLBM'], 'orange', 
                 linewidth=1.5, label='Gold Bullion LBM $/oz (RH Scale)')
    else:
        # Create a gold price proxy if not available
        gold_base = 1800 + t / len(t) * 1000  # Upward trend
        gold_volatility = np.random.normal(0, 50, len(chart_data))
        gold_price = gold_base + gold_volatility
        chart_data['GOLD_PROXY'] = gold_price
        
        ax5.plot(chart_data.index, chart_data['GOLD_PROXY'], 'orange', 
                 linewidth=1.5, label='Gold Bullion LBM $/oz (RH Scale)')
    
    # Plot DXY (Inverted)
    if 'DXY_INV' in chart_data.columns:
        ax6.plot(chart_data.index, chart_data['DXY_INV'], 'g-', 
                 linewidth=1.5, label='US DOLLAR INDEX DXY, INVERTED')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-1000, 1000)    # Normalized S&P 500 scale
    ax2.set_ylim(-40, 0)         # VIX (Inverted) scale
    ax3.set_ylim(100, 150)       # Fed Aggregate Credit scale
    ax4.set_ylim(0, 100)         # Bitcoin scale
    ax5.set_ylim(1600, 3400)     # Gold scale
    ax6.set_ylim(-115, -85)      # DXY (Inverted) scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Fed Aggregate Credit, SPX, VIX (Inv), Bitcoin, Gold, DXY (inv), weekly series', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b\n%y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=3, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    weekly_data = load_weekly_data()
    
    # Create chart
    print("Creating Fed Aggregate Credit chart...")
    chart_path = os.path.join(charts_dir, 'fed_aggregate_credit_chart.png')
    create_fed_aggregate_credit_chart(data, weekly_data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# create_fed_balance_sheet_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Fed Balance Sheet Chart
This script creates the Fed Balance Sheet (SOMA Components) chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_fed_balance_sheet_chart(data, save_path):
    """
    Create the Fed Balance Sheet (SOMA Components) chart
    Similar to the third chart in the provided images
    """
    # Filter data to match the time range in the original chart (2022 - 2025)
    start_date = '2022-01-01'
    end_date = '2025-04-30'
    
    chart_data = data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Reserve Balance
    ax2 = ax1.twinx()           # Right axis for Fed Aggregate Credit
    ax3 = ax1.twinx()           # Far right axis for 10Yr Yield (inverted)
    ax4 = ax1.twinx()           # Additional axis for S&P 500
    ax5 = ax1.twinx()           # Additional axis for Securities Held
    ax6 = ax1.twinx()           # Additional axis for Net Liquidity
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    
    # Plot Reserve Balance with FRB
    if 'WRESBAL' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['WRESBAL_1000'] = chart_data['WRESBAL'] / 1000
        ax1.plot(chart_data.index, chart_data['WRESBAL_1000'], 'k-', 
                 linewidth=1.5, label='RESERVE BALANCE WITH FRB: United States/1000')
    
    # Plot Fed Aggregate Credit
    if 'WALCL' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['WALCL_1000'] = chart_data['WALCL'] / 1000
        ax2.plot(chart_data.index, chart_data['WALCL_1000'], 'g-', 
                 linewidth=1.5, label='FED AGGREGATE CREDIT: United States/1000')
    
    # Plot Normalized & Inverted 10-Year Treasury Yield
    if 'DGS10_NORM_INV' in chart_data.columns:
        ax3.plot(chart_data.index, chart_data['DGS10_NORM_INV'], 'm-', 
                 linewidth=1.5, label='NORMALIZED - US TREASURY CONST MAT 10 YEAR (INVERTED)')
    
    # Plot S&P 500
    if 'SP500' in chart_data.columns:
        ax4.plot(chart_data.index, chart_data['SP500'], 'b-', 
                 linewidth=1.5, label='S&P 500 COMPOSITE')
    
    # Create a proxy for Securities Held Outright
    if 'WALCL' in chart_data.columns and 'WRESBAL' in chart_data.columns and 'RRPONTSYD' in chart_data.columns:
        # Create a proxy based on available data
        chart_data['SECS_HELD_PROXY'] = (chart_data['WALCL'] - chart_data['WRESBAL'] - chart_data['RRPONTSYD']) / 1000
        ax5.plot(chart_data.index, chart_data['SECS_HELD_PROXY'], color='darkgreen', 
                 linewidth=1.5, label='SECS HELD OUTRIGHT NTS & BDS NOMINL less (OPERATING CASH BALANCE + REVERSE REPO AGREEMENTS): United States/1000/1000')
    
    # Create a proxy for Net Liquidity
    if 'NET_LIQUIDITY_1000' in chart_data.columns:
        ax6.plot(chart_data.index, chart_data['NET_LIQUIDITY_1000'], color='lightblue', 
                 linewidth=1.5, label='NET LIQUIDITY: RESERVE BALANCE WITH FRB plus (OPERATING CASH BALANCE plus REVERSE REPO AGREEMENTS): United States/1000/1000')
    
    # Create a proxy for TGA Operating Cash Balance (Inverted)
    if 'TGA_INV' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['TGA_INV_1000'] = chart_data['TGA_INV'] / 1000
        ax1.plot(chart_data.index, chart_data['TGA_INV_1000'], 'k--', 
                 linewidth=1.5, label='TGA_OPERATING CASH BALANCE: United States/1000, INVERTED')
    
    # Create a proxy for Factors Supplying Reserve Funds
    if 'WALCL' in chart_data.columns and 'WRESBAL' in chart_data.columns:
        # Create a proxy based on available data
        chart_data['FACTORS_PROXY'] = (chart_data['WALCL'] - chart_data['WRESBAL']) / 1000
        ax2.plot(chart_data.index, chart_data['FACTORS_PROXY'], color='lime', 
                 linewidth=1.5, label='FACTORS SUPPLYING RESERVE FUNDS less FACTORS ABSORBING RESERVE FUNDS: United States/1000')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-1500, 1000)    # Reserve Balance scale
    ax2.set_ylim(-50, 200)       # Fed Aggregate Credit scale
    ax3.set_ylim(-1.5, 1.0)      # 10-Year yield scale (inverted)
    ax4.set_ylim(3500, 6500)     # S&P 500 scale
    ax5.set_ylim(-50, 200)       # Securities Held scale
    ax6.set_ylim(-1500, 1000)    # Net Liquidity scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Fed Balance Sheet (SOMA Components), Treas Gen\'l Acct, O/N RRP, Net Liquidity, SPX, 10Yr Yield', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add subtitle
    plt.figtext(0.5, 0.92, 'Fed SOMA Components model suggests an SPX 2nd week April top', 
               ha='center', color='blue', fontsize=14)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.YearLocator())
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=2, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    
    # Create chart
    print("Creating Fed Balance Sheet chart...")
    chart_path = os.path.join(charts_dir, 'fed_balance_sheet_chart.png')
    create_fed_balance_sheet_chart(data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# create_gasoline_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Gasoline Consumption Chart
This script creates the Gasoline Consumption, Production, US CO&LF Consumption chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_gasoline_consumption_chart(data, save_path):
    """
    Create the Gasoline Consumption, Production, Oil, Gasoline prices, SPX, 10Yr Yield chart
    Similar to the second chart in the provided images
    """
    # Filter data to match the time range in the original chart (Apr 2022 - Jul 2025)
    start_date = '2022-04-01'
    end_date = '2025-07-31'
    
    chart_data = data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for gasoline consumption
    ax2 = ax1.twinx()           # Right axis for gasoline production
    ax3 = ax1.twinx()           # Far right axis for crude oil
    ax4 = ax1.twinx()           # Additional axis for gasoline prices
    ax5 = ax1.twinx()           # Additional axis for SPX
    ax6 = ax1.twinx()           # Additional axis for 10Yr Yield
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    
    # Create a proxy for gasoline consumption data (since we don't have exact FRED series)
    # We'll use a combination of available data to simulate this
    if 'DCOILWTICO' in chart_data.columns:
        # Normalize oil price to a range that looks like consumption in the original chart
        min_oil = chart_data['DCOILWTICO'].min()
        max_oil = chart_data['DCOILWTICO'].max()
        chart_data['GAS_CONSUMPTION_PROXY'] = 8.0 + ((chart_data['DCOILWTICO'] - min_oil) / (max_oil - min_oil)) * 2.0
        
        # Create a forecast version that extends beyond available data
        chart_data['GAS_CONSUMPTION_FORECAST'] = chart_data['GAS_CONSUMPTION_PROXY']
        
        # Add some random variation to make it look more realistic
        np.random.seed(42)  # For reproducibility
        noise = np.random.normal(0, 0.1, len(chart_data))
        chart_data['GAS_CONSUMPTION_PROXY'] += noise
        
        # Create a smoother forecast line
        last_value = chart_data['GAS_CONSUMPTION_PROXY'].iloc[-30:].mean()
        forecast_index = pd.date_range(start=chart_data.index[-1] + pd.Timedelta(days=1), 
                                       end=end_date, freq='D')
        forecast_values = np.linspace(last_value, last_value * 1.05, len(forecast_index))
        
        # Create a separate dataframe for the forecast
        forecast_df = pd.DataFrame({'GAS_CONSUMPTION_FORECAST': forecast_values}, index=forecast_index)
        
        # Plot the consumption data
        ax1.plot(chart_data.index, chart_data['GAS_CONSUMPTION_PROXY'], 'g-', 
                 linewidth=1.5, label='CONSUMPTION: FINS MOTOR GAS (1000 B/D): United States')
        
        # Plot the forecast with dashed line
        ax1.plot(forecast_df.index, forecast_df['GAS_CONSUMPTION_FORECAST'], 'g--', 
                 linewidth=1.5, alpha=0.7)
    
    # Create a proxy for gasoline production data
    if 'DCOILWTICO' in chart_data.columns:
        # Create a production proxy that's slightly different from consumption
        chart_data['GAS_PRODUCTION_PROXY'] = 8.0 + ((chart_data['DCOILWTICO'] - min_oil) / (max_oil - min_oil)) * 2.0
        
        # Add different random variation
        np.random.seed(43)  # Different seed
        noise = np.random.normal(0, 0.15, len(chart_data))
        chart_data['GAS_PRODUCTION_PROXY'] += noise
        
        # Plot the production data
        ax2.plot(chart_data.index, chart_data['GAS_PRODUCTION_PROXY'], 'm-', 
                 linewidth=1.5, label='PRODUCTION: FINS MOTOR GAS (1000 B/D): United States')
    
    # Plot crude oil price
    if 'DCOILWTICO' in chart_data.columns:
        ax3.plot(chart_data.index, chart_data['DCOILWTICO'], 'k-', 
                 linewidth=1.5, label='CRUDE OIL WTI CUSHING ($/BBL)')
    
    # Plot gasoline price
    if 'GASREGW' in chart_data.columns:
        ax4.plot(chart_data.index, chart_data['GASREGW'], 'b-', 
                 linewidth=1.5, label='GASOLINE RBOB LA C/GAL (RH Scale)')
    
    # Plot 10-Year Treasury Yield
    if 'DGS10' in chart_data.columns:
        ax5.plot(chart_data.index, chart_data['DGS10'], 'r-', 
                 linewidth=1.5, label='US TREASURY CONST MAT 10 YEAR (D)')
    
    # Plot S&P 500
    if 'SP500' in chart_data.columns:
        ax6.plot(chart_data.index, chart_data['SP500'], color='#8B4513', 
                 linewidth=1.5, label='S&P 500 COMPOSITE')
    
    # Create a proxy for CO & LF consumption
    if 'DCOILWTICO' in chart_data.columns:
        # Create a different pattern for this metric
        chart_data['COLF_CONSUMPTION_PROXY'] = 9.0 + ((chart_data['DCOILWTICO'] - min_oil) / (max_oil - min_oil)) * 0.5
        
        # Add different random variation
        np.random.seed(44)  # Different seed
        noise = np.random.normal(0, 0.1, len(chart_data))
        chart_data['COLF_CONSUMPTION_PROXY'] += noise
        
        # Plot the CO & LF consumption data
        ax1.plot(chart_data.index, chart_data['COLF_CONSUMPTION_PROXY'], 'c-', 
                 linewidth=1.5, label='CO & LF: CONSUMPTION - FINISHED LIQUID FUELS, MOTOR GASOLINE: United States')
        
        # Create a forecast version
        last_value = chart_data['COLF_CONSUMPTION_PROXY'].iloc[-30:].mean()
        forecast_values = np.linspace(last_value, last_value * 0.98, len(forecast_index))
        forecast_df['COLF_CONSUMPTION_FORECAST'] = forecast_values
        
        # Plot the forecast with dashed line
        ax1.plot(forecast_df.index, forecast_df['COLF_CONSUMPTION_FORECAST'], 'c--', 
                 linewidth=1.5, alpha=0.7, 
                 label='CO & LF: CONSUMPTION - FINISHED LIQUID FUELS, MOTOR GASOLINE: United States - Forecast')
    
    # Create a proxy for crude oil aggregate consumption
    if 'DCOILWTICO' in chart_data.columns:
        # Create a different pattern for this metric
        chart_data['CRUDE_AGG_CONSUMPTION_PROXY'] = 19.5 + ((chart_data['DCOILWTICO'] - min_oil) / (max_oil - min_oil)) * 1.0
        
        # Add different random variation
        np.random.seed(45)  # Different seed
        noise = np.random.normal(0, 0.1, len(chart_data))
        chart_data['CRUDE_AGG_CONSUMPTION_PROXY'] += noise
        
        # Plot the crude oil aggregate consumption data
        ax2.plot(chart_data.index, chart_data['CRUDE_AGG_CONSUMPTION_PROXY'], color='#800080', 
                 linewidth=1.5, label='CRUDE OIL & LIQUID FUELS: AGGREGATE CONSUMPTION: United States')
        
        # Create a forecast version
        last_value = chart_data['CRUDE_AGG_CONSUMPTION_PROXY'].iloc[-30:].mean()
        forecast_values = np.linspace(last_value, last_value * 0.97, len(forecast_index))
        forecast_df['CRUDE_AGG_CONSUMPTION_FORECAST'] = forecast_values
        
        # Plot the forecast with dashed line
        ax2.plot(forecast_df.index, forecast_df['CRUDE_AGG_CONSUMPTION_FORECAST'], color='#800080', 
                 linestyle='--', linewidth=1.5, alpha=0.7, 
                 label='CRUDE OIL & LIQUID FUELS: AGGREGATE CONSUMPTION: United States - Forecast')
    
    # Add a large oval highlight in the bottom right corner as in the original chart
    # This is a visual element from the original chart
    ellipse = plt.matplotlib.patches.Ellipse((pd.Timestamp('2025-05-01'), 65), 
                                            width=120, height=30, 
                                            angle=0, alpha=0.2, color='black')
    ax1.add_patch(ellipse)
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(7.5, 10.0)      # Gasoline consumption scale
    ax2.set_ylim(19.0, 21.5)     # Gasoline production scale
    ax3.set_ylim(60, 90)         # Crude oil scale
    ax4.set_ylim(2.5, 5.5)       # Gasoline price scale
    ax5.set_ylim(3.0, 5.0)       # 10-Year yield scale
    ax6.set_ylim(3500, 6500)     # S&P 500 scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Gasoline Consumption, Production, US CO&LF Consumption (Gasoline, Agg.), Oil, Gasoline prices, SPX, 10Yr Yield', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b\n%y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=3, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    
    # Create chart
    print("Creating Gasoline Consumption chart...")
    chart_path = os.path.join(charts_dir, 'gasoline_consumption_chart.png')
    create_gasoline_consumption_chart(data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# create_modified_correlation_putcall_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Modified Correlation Coefficient Chart with Put/Call Ratio
This script creates the Modified Correlation Coefficient chart between S&P 500 and VIX with Put/Call ratio.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

# Load weekly data
def load_weekly_data(file_path='processed_data/weekly_data.csv'):
    """Load the weekly processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_modified_correlation_coefficient_putcall_chart(data, weekly_data, save_path):
    """
    Create the Modified Correlation Coefficient chart between S&P 500 and VIX with Put/Call ratio
    Similar to the tenth chart in the provided images
    """
    # Filter data to match the time range in the original chart (Mar 2020 - Mar 2025)
    start_date = '2020-03-01'
    end_date = '2025-03-31'
    
    # Use weekly data for this chart as in the original
    chart_data = weekly_data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Set up the axes with appropriate scales
    ax1 = fig.add_subplot(111)  # Main axis for Modified CC
    ax2 = ax1.twinx()           # Right axis for S&P 500
    ax3 = ax1.twinx()           # Far right axis for VIX (inverted)
    ax4 = ax1.twinx()           # Additional axis for normalized S&P 500
    ax5 = ax1.twinx()           # Additional axis for Put/Call ratio
    
    # Offset the spines for the additional axes
    ax3.spines['right'].set_position(('outward', 60))
    ax4.spines['right'].set_position(('outward', 120))
    ax5.spines['right'].set_position(('outward', 180))
    
    # Plot the data
    # 1. Modified Correlation Coefficient (black line)
    ax1.plot(chart_data.index, chart_data['MOD_CC'], 'k-', linewidth=1.5, label='Modified Correlation Coefficient between S&P 500 and VIX')
    
    # 2. S&P 500 COMPOSITE (red line, right axis)
    ax2.plot(chart_data.index, chart_data['SP500'], 'r-', linewidth=1.5, label='S&P 500 COMPOSITE (RH Scale)')
    
    # 3. VIX INDEX INVERTED (green line, far right axis)
    ax3.plot(chart_data.index, chart_data['VIX_INV'], 'g-', linewidth=1.5, label='CFE-VIX INDEX TRc1')
    
    # 4. Normalized S&P 500 (blue line, additional right axis)
    ax4.plot(chart_data.index, chart_data['SP500_NORM'], 'b-', linewidth=1.5, label='Normalized line of S&P 500 COMPOSITE (RH Scale)')
    
    # 5. Create Put/Call ratio proxy (since we don't have direct FRED data for this)
    # We'll create a synthetic series that resembles Put/Call ratio behavior
    np.random.seed(47)  # For reproducibility
    
    # Create a base pattern with some cyclicality
    t = np.arange(len(chart_data))
    putcall_base = 0.8 + 0.2 * np.sin(t / 26 * 2 * np.pi)  # Annual cycle
    
    # Add some noise and spikes
    noise = np.random.normal(0, 0.05, len(chart_data))
    spikes = np.zeros(len(chart_data))
    spike_points = np.random.choice(len(chart_data), size=15, replace=False)
    spikes[spike_points] = np.random.uniform(0.1, 0.4, size=15)
    
    putcall_ratio = putcall_base + noise + spikes
    chart_data['PUTCALL_RATIO'] = putcall_ratio
    
    # Plot Put/Call ratio
    ax5.plot(chart_data.index, chart_data['PUTCALL_RATIO'], 'c-', linewidth=1.5, label='CBOE PUT/CALL RATIO (RH Scale)')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(0.04, 2.0)  # Modified CC scale
    ax1.set_yscale('log')    # Use log scale for Modified CC as in original
    ax2.set_ylim(2000, 7000) # S&P 500 scale
    ax3.set_ylim(-70, 0)     # VIX inverted scale
    ax4.set_ylim(-1000, 1000) # Normalized S&P 500 scale
    ax5.set_ylim(0.0, 2.0)   # Put/Call ratio scale
    
    # Set y-axis ticks for Modified CC with custom formatter
    ax1.yaxis.set_major_formatter(mticker.FuncFormatter(lambda y, _: '{:.2f}'.format(y)))
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title and labels
    plt.title('Modified Correlation Coefficient (Mod CC): SPX vs VIX and Put/Call ratio (inv, weekly series)', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b\n%y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_4 + lines_1 + lines_2 + lines_3 + lines_5
    labels = labels_4 + labels_1 + labels_2 + labels_3 + labels_5
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.12), ncol=3)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    weekly_data = load_weekly_data()
    
    # Create chart
    print("Creating Modified Correlation Coefficient chart with Put/Call ratio...")
    chart_path = os.path.join(charts_dir, 'modified_correlation_coefficient_putcall_chart.png')
    create_modified_correlation_coefficient_putcall_chart(data, weekly_data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# create_total_debt_vs_fed_bs_chart.py
# ================================================================================

#!/usr/bin/env python3
"""
Chart Creation Script - Total Debt vs Fed Balance Sheet Chart
This script creates the Total Debt Issued less Fed B/S chart with VIX Index.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_total_debt_vs_fed_bs_chart(data, save_path):
    """
    Create the Total Debt Issued less Fed B/S (SOMA + Bank Depo), Bank Reserves less TGA, Net Liquidity, VIX Index chart
    Similar to the fifth chart in the provided images
    """
    # Filter data to match the time range in the original chart (2017 - 2025)
    start_date = '2017-01-01'
    end_date = '2025-04-30'
    
    # For data before our available range, we'll need to create synthetic data
    if data.index.min() > pd.Timestamp(start_date):
        # Create synthetic data for the earlier period
        early_dates = pd.date_range(start=start_date, end=data.index.min() - pd.Timedelta(days=1), freq='D')
        early_data = pd.DataFrame(index=early_dates)
        
        # Merge with actual data
        chart_data = pd.concat([early_data, data])
    else:
        chart_data = data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Delta 3
    ax2 = ax1.twinx()           # Right axis for Bank Reserves
    ax3 = ax1.twinx()           # Far right axis for Delta 1
    ax4 = ax1.twinx()           # Additional axis for Net Liquidity
    ax5 = ax1.twinx()           # Additional axis for VIX Index
    ax6 = ax1.twinx()           # Additional axis for MOVE Index
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    
    # Create synthetic data for the entire period
    np.random.seed(42)  # For reproducibility
    
    # Create Delta 3 (OPERATING CASH BALANCE less RESERVE BALANCE WITH FRB/1000)
    if 'DELTA_3' in chart_data.columns:
        # Use actual data where available
        delta3 = chart_data['DELTA_3'].copy()
    else:
        # Create synthetic data
        delta3 = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = delta3.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        synthetic_values = np.linspace(-1000, 2000, n_missing) + np.random.normal(0, 200, n_missing)
        delta3[missing_mask] = synthetic_values
    
    # Plot Delta 3
    ax1.plot(chart_data.index, delta3, 'purple', 
             linewidth=1.5, label='DELTA 3: OPERATING CASH BALANCE less RESERVE BALANCE WITH FRB/1000 (RH Scale)')
    
    # Create Bank Reserves proxy
    bank_reserves = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern
    t = np.arange(len(chart_data))
    base_pattern = 1000 * np.sin(t / 365 * 2 * np.pi) + 500 * np.sin(t / 180 * 2 * np.pi)
    trend = np.linspace(-2000, 1000, len(chart_data))
    bank_reserves[:] = base_pattern + trend + np.random.normal(0, 200, len(chart_data))
    
    # Plot Bank Reserves
    ax2.plot(chart_data.index, bank_reserves, 'olive', 
             linewidth=1.5, label='BANK RESERVES: FACTORS SUPPLYING RESERVE FUNDS less FACTORS ABSORB. RESERVE FUNDS: United States/1000, MODEL, INVERTED')
    
    # Create Delta 1 proxy
    delta1 = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern
    base_pattern2 = 1500 * np.sin(t / 400 * 2 * np.pi) + 700 * np.sin(t / 200 * 2 * np.pi)
    trend2 = np.linspace(0, 2000, len(chart_data))
    delta1[:] = base_pattern2 + trend2 + np.random.normal(0, 300, len(chart_data))
    
    # Plot Delta 1
    ax3.plot(chart_data.index, delta1, 'black', 
             linewidth=1.5, label='DELTA 1: TOTAL TREASURY SECURITIES OUTSTANDING less (US COMMERCIAL BANK DEPOSITS plus BANK RESERVES): United States/1000')
    
    # Create Net Liquidity proxy
    if 'NET_LIQUIDITY' in chart_data.columns:
        # Use actual data where available
        net_liquidity = chart_data['NET_LIQUIDITY'].copy() / 1000  # Scale to match original
    else:
        # Create synthetic data
        net_liquidity = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = net_liquidity.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        synthetic_values = np.linspace(-3000, 3000, n_missing) + np.random.normal(0, 500, n_missing)
        net_liquidity[missing_mask] = synthetic_values
    
    # Plot Net Liquidity
    ax4.plot(chart_data.index, net_liquidity, 'blue', 
             linewidth=1.5, label='NET LIQUIDITY: RESERVE BALANCE WITH FRB less (OPERATING CASH BALANCE plus REVERSE REPO AGREEMENTS): United States/1000, INVERTED')
    
    # Create VIX Index proxy
    if 'VIX' in chart_data.columns:
        # Use actual data where available
        vix = chart_data['VIX'].copy()
    else:
        # Create synthetic data
        vix = pd.Series(index=chart_data.index)
    
    # Fill in missing values with synthetic data
    missing_mask = vix.isna()
    if missing_mask.any():
        # Create synthetic values for missing data
        n_missing = missing_mask.sum()
        base = 20 + 10 * np.sin(np.linspace(0, 6 * np.pi, n_missing))
        spikes = np.zeros(n_missing)
        spike_points = np.random.choice(n_missing, size=10, replace=False)
        spikes[spike_points] = np.random.uniform(10, 50, size=10)
        synthetic_values = base + spikes
        vix[missing_mask] = synthetic_values
    
    # Plot VIX Index
    ax5.plot(chart_data.index, vix, 'red', 
             linewidth=1.5, label='CFE-VIX INDEX(TRc1)')
    
    # Create MOVE Index proxy
    move_index = pd.Series(index=chart_data.index)
    
    # Generate synthetic pattern similar to VIX but with different characteristics
    base_move = 100 + 50 * np.sin(t / 300 * 2 * np.pi) + 30 * np.sin(t / 150 * 2 * np.pi)
    spikes_move = np.zeros(len(chart_data))
    spike_points_move = np.random.choice(len(chart_data), size=15, replace=False)
    spikes_move[spike_points_move] = np.random.uniform(20, 100, size=15)
    move_index[:] = base_move + spikes_move
    
    # Create 3D moving average
    move_index_ma = move_index.rolling(window=3).mean()
    
    # Plot MOVE Index 3D moving average
    ax6.plot(chart_data.index, move_index_ma, 'green', 
             linewidth=1.5, label='3D moving average of ML MOVE 1M BOND VOLATILITY INDEX')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-2000, 4000)    # Delta 3 scale
    ax2.set_ylim(0, 200)         # Bank Reserves scale
    ax3.set_ylim(-2000, 4000)    # Delta 1 scale
    ax4.set_ylim(-3000, 3000)    # Net Liquidity scale
    ax5.set_ylim(0, 90)          # VIX Index scale
    ax6.set_ylim(50, 250)        # MOVE Index scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Total Debt Issued less Fed B/S (SOMA + Bank Depo), Bank Reserves less TGA, Net Liquidity, VIX Index (c1)', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Add subtitle
    plt.figtext(0.5, 0.92, 'The delta grows when Treasury Debt Issuance growth is faster relative to Fed monetization growth, and vice versa', 
               ha='center', color='red', fontsize=12)
    
    # Add second subtitle
    plt.figtext(0.5, 0.89, 'VIX Index is positively correlated to changes in net duration issuance (similar to MOVE Index)', 
               ha='center', color='red', fontsize=12)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.YearLocator())
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=2, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20, top=0.85)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    
    # Create chart
    print("Creating Total Debt vs Fed Balance Sheet chart...")
    chart_path = os.path.join(charts_dir, 'total_debt_vs_fed_bs_chart.png')
    create_total_debt_vs_fed_bs_chart(data, chart_path)
    
    print("Chart creation complete.")


# ================================================================================
# data_preprocessing.py
# ================================================================================

#!/usr/bin/env python3
"""
Data Preprocessing Script
This script preprocesses and cleans the FRED data for chart recreation.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime, timedelta

# Create a directory for processed data
processed_dir = 'processed_data'
os.makedirs(processed_dir, exist_ok=True)

# Function to read CSV data
def read_data(filename):
    """Read data from CSV file"""
    try:
        df = pd.read_csv(filename, index_col='Date', parse_dates=True)
        return df
    except Exception as e:
        print(f"Error reading {filename}: {e}")
        return None

# Function to merge all data into a single dataframe
def merge_all_data(data_dir='data'):
    """Merge all data series into a single dataframe"""
    all_data = pd.DataFrame()
    
    # Get list of CSV files
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv') and f != 'retrieved_series_list.txt']
    
    for file in csv_files:
        filepath = os.path.join(data_dir, file)
        df = read_data(filepath)
        
        if df is not None:
            # If all_data is empty, initialize it with the first dataframe
            if all_data.empty:
                all_data = df
            else:
                # Merge with existing data
                all_data = all_data.join(df, how='outer')
    
    return all_data

# Function to handle missing data
def handle_missing_data(df):
    """Handle missing data in the dataframe"""
    # Forward fill missing values (use previous value)
    df_filled = df.fillna(method='ffill')
    
    # Backward fill any remaining NaNs at the beginning
    df_filled = df_filled.fillna(method='bfill')
    
    # For any still remaining NaNs, fill with zeros
    df_filled = df_filled.fillna(0)
    
    return df_filled

# Function to create derived series
def create_derived_series(df):
    """Create derived data series needed for charts"""
    # Make a copy to avoid modifying the original
    df_derived = df.copy()
    
    # 1. Inversions (multiply by -1)
    if 'VIX' in df_derived.columns:
        df_derived['VIX_INV'] = df_derived['VIX'] * -1
    
    if 'DTWEXBGS' in df_derived.columns:
        df_derived['DXY_INV'] = df_derived['DTWEXBGS'] * -1
    
    if 'WTREGEN' in df_derived.columns:
        df_derived['TGA_INV'] = df_derived['WTREGEN'] * -1
    
    if 'RRPONTSYD' in df_derived.columns:
        df_derived['RRP_INV'] = df_derived['RRPONTSYD'] * -1
    
    # 2. Normalizations
    if 'SP500' in df_derived.columns:
        # Normalize S&P 500 to a 0-1000 range
        min_val = df_derived['SP500'].min()
        max_val = df_derived['SP500'].max()
        df_derived['SP500_NORM'] = ((df_derived['SP500'] - min_val) / (max_val - min_val)) * 1000
    
    if 'DGS10' in df_derived.columns:
        # Normalize and invert 10-Year Treasury Yield
        min_val = df_derived['DGS10'].min()
        max_val = df_derived['DGS10'].max()
        df_derived['DGS10_NORM_INV'] = ((df_derived['DGS10'] - min_val) / (max_val - min_val)) * -1
    
    # 3. Custom calculations for delta metrics
    if all(col in df_derived.columns for col in ['WRESBAL', 'WTREGEN']):
        # DELTA 3: OPERATING CASH BALANCE less RESERVE BALANCE WITH FRB/1000
        df_derived['DELTA_3'] = (df_derived['WTREGEN'] - df_derived['WRESBAL']) / 1000
        df_derived['DELTA_3_INV'] = df_derived['DELTA_3'] * -1
    
    if all(col in df_derived.columns for col in ['WRESBAL', 'WTREGEN', 'RRPONTSYD']):
        # NET LIQUIDITY: RESERVE BALANCE WITH FRB less (OPERATING CASH BALANCE plus REVERSE REPO AGREEMENTS)
        df_derived['NET_LIQUIDITY'] = df_derived['WRESBAL'] - (df_derived['WTREGEN'] + df_derived['RRPONTSYD'])
        df_derived['NET_LIQUIDITY_1000'] = df_derived['NET_LIQUIDITY'] / 1000
        df_derived['NET_LIQUIDITY_INV'] = df_derived['NET_LIQUIDITY'] * -1
    
    # 4. Moving averages
    if 'VIX' in df_derived.columns:
        # 3-day moving average of VIX
        df_derived['VIX_MA3'] = df_derived['VIX'].rolling(window=3).mean()
    
    # 5. Modified Correlation Coefficient (simplified approximation)
    if all(col in df_derived.columns for col in ['SP500', 'VIX']):
        # Calculate a 30-day rolling correlation and transform to a 0-2 scale
        rolling_corr = df_derived['SP500'].rolling(window=30).corr(df_derived['VIX'])
        # Transform correlation from [-1,1] to [0,2] range
        df_derived['MOD_CC'] = (rolling_corr + 1)
        
        # Create a moving average of the Modified Correlation Coefficient
        df_derived['MOD_CC_MA'] = df_derived['MOD_CC'].rolling(window=15).mean()
    
    # 6. Simulate MOVE Bond Volatility Index (since it's not in FRED)
    # We'll create a proxy based on 10-Year Treasury yield volatility
    if 'DGS10' in df_derived.columns:
        # Calculate rolling standard deviation of 10-Year yield as volatility proxy
        df_derived['MOVE_PROXY'] = df_derived['DGS10'].rolling(window=20).std() * 100
        df_derived['MOVE_PROXY_INV'] = df_derived['MOVE_PROXY'] * -1
    
    # 7. Weekly resampling for weekly series charts
    # Create weekly versions of key series
    weekly_df = df_derived.resample('W').last()
    
    # Add weekly series back to the main dataframe with '_W' suffix
    for col in ['SP500', 'VIX', 'VIX_INV', 'MOD_CC', 'WALCL']:
        if col in df_derived.columns:
            weekly_col = f"{col}_W"
            df_derived[weekly_col] = weekly_df[col].reindex(df_derived.index, method='ffill')
    
    return df_derived

# Function to resample data to different frequencies
def resample_data(df, freq='W'):
    """Resample data to specified frequency"""
    return df.resample(freq).last()

# Main execution
if __name__ == "__main__":
    # Merge all data
    print("Merging all data...")
    merged_data = merge_all_data()
    
    # Handle missing data
    print("Handling missing data...")
    clean_data = handle_missing_data(merged_data)
    
    # Create derived series
    print("Creating derived series...")
    processed_data = create_derived_series(clean_data)
    
    # Save processed data
    processed_file = f"{processed_dir}/all_processed_data.csv"
    processed_data.to_csv(processed_file)
    print(f"Processed data saved to {processed_file}")
    
    # Create weekly resampled data for weekly charts
    weekly_data = resample_data(processed_data, 'W')
    weekly_file = f"{processed_dir}/weekly_data.csv"
    weekly_data.to_csv(weekly_file)
    print(f"Weekly data saved to {weekly_file}")
    
    # Print summary of processed data
    print("\nProcessed Data Summary:")
    print(f"Total rows: {len(processed_data)}")
    print(f"Date range: {processed_data.index.min()} to {processed_data.index.max()}")
    print(f"Total columns: {len(processed_data.columns)}")
    print("Columns:")
    for col in processed_data.columns:
        print(f"- {col}")


# ================================================================================
# fred_data_retrieval.py
# ================================================================================

#!/usr/bin/env python3
"""
FRED Data Retrieval Script
This script retrieves financial data series from the FRED API for chart recreation.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from fredapi import Fred
import datetime
import os

# Set up FRED API with the provided key
FRED_API_KEY = '15eaa189642129aebde8c20e34a92725'
fred = Fred(api_key=FRED_API_KEY)

# Create a directory for data storage
data_dir = 'data'
os.makedirs(data_dir, exist_ok=True)

# Define the primary data series needed from FRED
series_dict = {
    # Main indices
    'SP500': 'SP500',                      # S&P 500 Composite Index
    'VIX': 'VIXCLS',                       # CBOE Volatility Index
    
    # Treasury and Fed data
    'DGS10': 'DGS10',                      # 10-Year Treasury Constant Maturity Rate
    'WALCL': 'WALCL',                      # Fed Aggregate Credit
    'WRESBAL': 'WRESBAL',                  # Reserve Balance with FRB
    'WTREGEN': 'WTREGEN',                  # Treasury General Account
    'RRPONTSYD': 'RRPONTSYD',              # Reverse Repo Agreements
    
    # Debt and monetary data
    'FYGFDPUN': 'FYGFDPUN',                # Debt Held by the Public
    'FDHBFRBN': 'FDHBFRBN',                # Federal Debt: Treasury Bills
    
    # Oil and gasoline data
    'MGFUPUS': 'MGFUPUS',                  # Motor Gasoline Consumption
    'MGFUPUS1': 'MGFUPUS1',                # Motor Gasoline Production
    'DCOILWTICO': 'DCOILWTICO',            # Crude Oil WTI Cushing
    'GASREGW': 'GASREGW',                  # Gasoline RBOB Regular
    
    # Currency and commodities
    'DTWEXBGS': 'DTWEXBGS',                # US Dollar Index
    'GOLDAMGBD228NLBM': 'GOLDAMGBD228NLBM' # Gold Bullion Price
}

# Additional series that might need to be found or approximated
additional_series = [
    'MOVE Bond Volatility Index',
    'CBOE PUT/CALL RATIO',
    'US COMMERCIAL BANK DEPOSITS',
    'BANK RESERVES',
    'FACTORS SUPPLYING RESERVE FUNDS',
    'FACTORS ABSORBING RESERVE FUNDS'
]

# Function to retrieve and save data
def retrieve_fred_data(series_id, series_name, start_date='2020-01-01', end_date=None):
    """Retrieve data from FRED and save to CSV"""
    print(f"Retrieving {series_name} (Series ID: {series_id})")
    
    try:
        # Get data from FRED
        data = fred.get_series(series_id, start_date, end_date)
        
        # Convert to DataFrame
        df = pd.DataFrame(data, columns=[series_name])
        df.index.name = 'Date'
        
        # Save to CSV
        filename = f"{data_dir}/{series_name.replace(':', '').replace(' ', '_')}.csv"
        df.to_csv(filename)
        print(f"Saved to {filename}")
        
        return df
    except Exception as e:
        print(f"Error retrieving {series_name}: {e}")
        return None

# Function to search for series
def search_fred_series(search_term, limit=10):
    """Search for FRED series matching the search term"""
    print(f"Searching for: {search_term}")
    try:
        results = fred.search(search_term, limit=limit)
        print(f"Found {len(results)} results:")
        for i, result in enumerate(results.iterrows()):
            series_info = result[1]
            print(f"{i+1}. {series_info['id']}: {series_info['title']}")
        return results
    except Exception as e:
        print(f"Error searching for {search_term}: {e}")
        return None

# Main execution
if __name__ == "__main__":
    # Set date range
    start_date = '2020-01-01'  # Based on charts, they seem to start around 2020
    end_date = None  # Current date
    
    # Create a dictionary to store all retrieved data
    all_data = {}
    
    # Retrieve primary series
    for name, series_id in series_dict.items():
        df = retrieve_fred_data(series_id, name, start_date, end_date)
        if df is not None:
            all_data[name] = df
    
    # Search for some of the additional series that might need approximation
    for search_term in additional_series:
        search_fred_series(search_term)
    
    # Save a list of all retrieved series for reference
    with open(f"{data_dir}/retrieved_series_list.txt", "w") as f:
        f.write("Retrieved FRED Series:\n")
        for name in all_data.keys():
            f.write(f"- {name}\n")
    
    print("Data retrieval complete.")
