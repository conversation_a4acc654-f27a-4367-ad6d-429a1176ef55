#!/usr/bin/env python3
"""
Chart Creation Script - Fed Aggregate Credit Chart
This script creates the Fed Aggregate Credit, SPX, VIX (Inv), Bitcoin, Gold, DXY (inv) chart.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import matplotlib.ticker as mticker
from matplotlib.gridspec import GridSpec
import os

# Create directory for charts
charts_dir = 'charts'
os.makedirs(charts_dir, exist_ok=True)

# Load processed data
def load_processed_data(file_path='processed_data/all_processed_data.csv'):
    """Load the processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

# Load weekly data
def load_weekly_data(file_path='processed_data/weekly_data.csv'):
    """Load the weekly processed data from CSV"""
    return pd.read_csv(file_path, index_col='Date', parse_dates=True)

def create_fed_aggregate_credit_chart(data, weekly_data, save_path):
    """
    Create the Fed Aggregate Credit, SPX, VIX (Inv), Bitcoin, Gold, DXY (inv) chart
    Similar to the ninth chart in the provided images
    """
    # Filter data to match the time range in the original chart (Oct 2022 - Jun 2025)
    start_date = '2022-10-01'
    end_date = '2025-06-30'
    
    # Use weekly data for this chart as in the original
    chart_data = weekly_data.loc[start_date:end_date].copy()
    
    # Create figure with custom size to match original
    fig = plt.figure(figsize=(16, 8))
    
    # Create multiple y-axes for different metrics
    ax1 = fig.add_subplot(111)  # Main axis for Normalized S&P 500
    ax2 = ax1.twinx()           # Right axis for VIX (Inverted)
    ax3 = ax1.twinx()           # Far right axis for Fed Aggregate Credit
    ax4 = ax1.twinx()           # Additional axis for Bitcoin
    ax5 = ax1.twinx()           # Additional axis for Gold
    ax6 = ax1.twinx()           # Additional axis for DXY (Inverted)
    
    # Offset the spines for the additional axes
    ax2.spines['right'].set_position(('outward', 60))
    ax3.spines['right'].set_position(('outward', 120))
    ax4.spines['right'].set_position(('outward', 180))
    ax5.spines['right'].set_position(('outward', 240))
    ax6.spines['right'].set_position(('outward', 300))
    
    # Plot Normalized S&P 500
    if 'SP500_NORM' in chart_data.columns:
        ax1.plot(chart_data.index, chart_data['SP500_NORM'], 'b-', 
                 linewidth=1.5, label='NORMALIZED - S&P 500 COMPOSITE')
    
    # Plot VIX (Inverted)
    if 'VIX_INV' in chart_data.columns:
        ax2.plot(chart_data.index, chart_data['VIX_INV'], 'm-', 
                 linewidth=1.5, label='CFE-VIX INDEX c1, INVERTED')
    
    # Plot Fed Aggregate Credit
    if 'WALCL' in chart_data.columns:
        # Divide by 1000 as in the original chart
        chart_data['WALCL_1000'] = chart_data['WALCL'] / 1000
        ax3.plot(chart_data.index, chart_data['WALCL_1000'], 'g-', 
                 linewidth=1.5, label='FED AGGREGATE CREDIT: United States/1000')
    
    # Create Bitcoin proxy (since we don't have direct FRED data for this)
    # We'll create a synthetic series that resembles Bitcoin's price movement
    np.random.seed(46)  # For reproducibility
    
    # Create a base trend with some volatility
    t = np.arange(len(chart_data))
    bitcoin_base = 20 + t / len(t) * 60  # Upward trend
    
    # Add some volatility and corrections
    volatility = np.random.normal(0, 5, len(chart_data))
    corrections = np.zeros(len(chart_data))
    
    # Add a few major corrections
    correction_points = [int(len(chart_data) * 0.2), int(len(chart_data) * 0.5), int(len(chart_data) * 0.8)]
    for point in correction_points:
        corrections[point:point+10] = -np.linspace(0, 15, 10)
        corrections[point+10:point+20] = np.linspace(-15, 0, 10)
    
    bitcoin_price = bitcoin_base + volatility + corrections
    chart_data['BITCOIN_PROXY'] = bitcoin_price
    
    # Plot Bitcoin proxy
    ax4.plot(chart_data.index, chart_data['BITCOIN_PROXY'], 'brown', 
             linewidth=1.5, label='GRAYSCALE BITCOIN TRUST ETF (RH Scale)')
    
    # Plot Gold
    if 'GOLDAMGBD228NLBM' in chart_data.columns:
        ax5.plot(chart_data.index, chart_data['GOLDAMGBD228NLBM'], 'orange', 
                 linewidth=1.5, label='Gold Bullion LBM $/oz (RH Scale)')
    else:
        # Create a gold price proxy if not available
        gold_base = 1800 + t / len(t) * 1000  # Upward trend
        gold_volatility = np.random.normal(0, 50, len(chart_data))
        gold_price = gold_base + gold_volatility
        chart_data['GOLD_PROXY'] = gold_price
        
        ax5.plot(chart_data.index, chart_data['GOLD_PROXY'], 'orange', 
                 linewidth=1.5, label='Gold Bullion LBM $/oz (RH Scale)')
    
    # Plot DXY (Inverted)
    if 'DXY_INV' in chart_data.columns:
        ax6.plot(chart_data.index, chart_data['DXY_INV'], 'g-', 
                 linewidth=1.5, label='US DOLLAR INDEX DXY, INVERTED')
    
    # Set y-axis limits to match original chart
    ax1.set_ylim(-1000, 1000)    # Normalized S&P 500 scale
    ax2.set_ylim(-40, 0)         # VIX (Inverted) scale
    ax3.set_ylim(100, 150)       # Fed Aggregate Credit scale
    ax4.set_ylim(0, 100)         # Bitcoin scale
    ax5.set_ylim(1600, 3400)     # Gold scale
    ax6.set_ylim(-115, -85)      # DXY (Inverted) scale
    
    # Set grid
    ax1.grid(True, alpha=0.3)
    
    # Set title
    plt.title('Fed Aggregate Credit, SPX, VIX (Inv), Bitcoin, Gold, DXY (inv), weekly series', 
              fontsize=16, fontweight='bold', pad=20)
    
    # Set x-axis to show dates nicely
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b\n%y'))
    
    # Create custom legend
    lines_1, labels_1 = ax1.get_legend_handles_labels()
    lines_2, labels_2 = ax2.get_legend_handles_labels()
    lines_3, labels_3 = ax3.get_legend_handles_labels()
    lines_4, labels_4 = ax4.get_legend_handles_labels()
    lines_5, labels_5 = ax5.get_legend_handles_labels()
    lines_6, labels_6 = ax6.get_legend_handles_labels()
    
    # Combine all lines and labels
    lines = lines_1 + lines_2 + lines_3 + lines_4 + lines_5 + lines_6
    labels = labels_1 + labels_2 + labels_3 + labels_4 + labels_5 + labels_6
    
    # Add legend at the bottom
    ax1.legend(lines, labels, loc='upper center', bbox_to_anchor=(0.5, -0.15), ncol=3, fontsize=8)
    
    # Add source text
    plt.figtext(0.85, 0.01, 'Source: FRED Data / Python Recreation', fontsize=8)
    
    # Adjust layout
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.20)
    
    # Save the figure
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {save_path}")
    
    # Close the figure to free memory
    plt.close(fig)
    
    return save_path

if __name__ == "__main__":
    # Load data
    print("Loading processed data...")
    data = load_processed_data()
    weekly_data = load_weekly_data()
    
    # Create chart
    print("Creating Fed Aggregate Credit chart...")
    chart_path = os.path.join(charts_dir, 'fed_aggregate_credit_chart.png')
    create_fed_aggregate_credit_chart(data, weekly_data, chart_path)
    
    print("Chart creation complete.")
