import os
import pandas as pd
import numpy as np
from fpdf import FPDF
import matplotlib.pyplot as plt
from matplotlib.ticker import ScalarFormatter
import matplotlib.dates as mdates

# Create directories if they don't exist
os.makedirs('report', exist_ok=True)

# Create a PDF document
class PDF(FPDF):
    def header(self):
        # Set font
        self.set_font('Arial', 'B', 12)
        # Title
        self.cell(0, 10, 'Financial Charts Analysis Report', 0, 1, 'C')
        # Line break
        self.ln(10)

    def footer(self):
        # Position at 1.5 cm from bottom
        self.set_y(-15)
        # Set font
        self.set_font('Arial', 'I', 8)
        # Page number
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')

    def chapter_title(self, title):
        # Set font
        self.set_font('Arial', 'B', 12)
        # Title
        self.cell(0, 10, title, 0, 1, 'L')
        # Line break
        self.ln(5)

    def chapter_body(self, body):
        # Set font
        self.set_font('Arial', '', 11)
        # Output text
        self.multi_cell(0, 10, body)
        # Line break
        self.ln()

    def add_chart(self, chart_path, caption):
        # Add chart image
        if os.path.exists(chart_path):
            self.image(chart_path, x=10, w=190)
            # Caption
            self.set_font('Arial', 'I', 10)
            self.cell(0, 10, caption, 0, 1, 'C')
            self.ln(5)
        else:
            self.set_font('Arial', 'B', 11)
            self.cell(0, 10, f"Chart not found: {chart_path}", 0, 1, 'C')
            self.ln(5)

# Create PDF
pdf = PDF()
pdf.set_auto_page_break(auto=True, margin=15)
pdf.add_page()

# Introduction
pdf.chapter_title('Introduction')
pdf.chapter_body(
    'This report presents a series of financial charts recreated using Python and data from the Federal Reserve Economic Data (FRED) API. '
    'The charts analyze various economic indicators including the S&P 500 index, VIX volatility index, gasoline consumption, '
    'Fed balance sheet components, debt issuance, and their interrelationships. '
    'Each chart provides insights into market dynamics and potential predictive relationships between these indicators.'
)

# 1. Modified Correlation Coefficient Chart (with proper extension into May)
pdf.add_page()
pdf.chapter_title('1. Modified Correlation Coefficient between S&P and VIX')
pdf.chapter_body(
    'This chart shows the Modified Correlation Coefficient (Mod CC) between the S&P 500 Composite Index and the VIX volatility index. '
    'The correlation has been shifted forward by 4 weeks to demonstrate its predictive relationship with market movements. '
    'The black line represents the Modified Correlation Coefficient, while the cyan line shows its moving average. '
    'Note how the correlation extends into May 2025, providing forward-looking insights, while the other indicators end in April 2025. '
    'This predictive relationship can be used to anticipate potential market tops and bottoms.'
)
pdf.add_chart('charts/modified_correlation_coefficient_chart_final.png', 
              'Figure 1: Modified Correlation Coefficient (Mod CC) between S&P Comp Index vs VIX (weekly series)')

# 2. Gasoline Consumption Chart
pdf.add_page()
pdf.chapter_title('2. Gasoline Consumption, Production, and Related Indicators')
pdf.chapter_body(
    'This chart displays the relationship between gasoline consumption, production, crude oil prices, and other related indicators. '
    'It shows how changes in gasoline consumption and production correlate with oil prices, the S&P 500, and 10-year Treasury yields. '
    'The chart highlights the complex interplay between energy markets and broader economic indicators.'
)
pdf.add_chart('charts/gasoline_consumption_chart.png', 
              'Figure 2: Gasoline Consumption, Production, US CO&LF Consumption, Oil, Gasoline prices, SPX, 10Yr Yield')

# 3. Fed Balance Sheet Chart
pdf.add_page()
pdf.chapter_title('3. Fed Balance Sheet (SOMA Components)')
pdf.chapter_body(
    'This chart analyzes the components of the Federal Reserve\'s System Open Market Account (SOMA) in relation to Treasury accounts, '
    'overnight reverse repo program (O/N RRP), net liquidity, the S&P 500, and 10-year Treasury yields. '
    'The chart demonstrates how changes in the Fed\'s balance sheet components can influence market liquidity and asset prices.'
)
pdf.add_chart('charts/fed_balance_sheet_chart.png', 
              'Figure 3: Fed Balance Sheet (SOMA Components), Treas Gen\'l Acct, O/N RRP, Net Liquidity, SPX, 10Yr Yield')

# 4. Debt Issuance Chart
pdf.add_page()
pdf.chapter_title('4. Debt Issuance, T-Bills, and Related Indicators')
pdf.chapter_body(
    'This chart examines the relationship between debt issuance, Treasury bills issued, overnight reverse repo program (O/N RRP), '
    'and 10-year Treasury yields. It illustrates how government debt management policies interact with interest rates and '
    'market liquidity conditions.'
)
pdf.add_chart('charts/debt_issuance_chart.png', 
              'Figure 4: Debt Issuance, T-Bills Issued, O/N RRP, 10Yr Yield')

# 5. Total Debt vs Fed Balance Sheet Chart
pdf.add_page()
pdf.chapter_title('5. Total Debt vs Fed Balance Sheet')
pdf.chapter_body(
    'This chart analyzes the relationship between total debt issued, the Federal Reserve\'s balance sheet, bank reserves, '
    'net liquidity, the VIX volatility index, and related metrics. The chart highlights how the delta (difference) between '
    'Treasury debt issuance and Fed monetization affects market volatility and risk assets.'
)
pdf.add_chart('charts/total_debt_vs_fed_bs_chart.png', 
              'Figure 5: Total Debt Issued less Fed B/S (SOMA + Bank Depo), Bank Reserves less TGA, Net Liquidity, VIX Index')

# 6. Delta of Fed Balance Sheet Chart
pdf.add_page()
pdf.chapter_title('6. Delta of Fed Balance Sheet and Market Indicators')
pdf.chapter_body(
    'This chart examines the delta (difference) between the Fed\'s balance sheet components and total debt issued, '
    'in relation to bank reserves, the Treasury General Account (TGA), 10-year Treasury yields, the US Dollar Index (DXY), '
    'and gold prices. The chart demonstrates how changes in this delta can influence various asset classes.'
)
pdf.add_chart('charts/delta_of_fed_bs_chart.png', 
              'Figure 6: Delta of Fed B/S (SOMA plus Bank Deposits), Total Debt Issued: Bank Reserves, TGA, 10Yr Yield, DXY, Gold')

# 7. Debt Issued, Fed B/S Chart
pdf.add_page()
pdf.chapter_title('7. Debt Issued, Fed Balance Sheet, and Market Indicators')
pdf.chapter_body(
    'This chart analyzes the relationship between debt issuance, the Fed\'s balance sheet, bank reserves, net liquidity, '
    'the MOVE bond volatility index, the S&P 500, and the VIX volatility index. It illustrates how changes in the growth rates '
    'of Treasury debt issuance relative to Fed monetization affect risk assets and market volatility.'
)
pdf.add_chart('charts/debt_issued_fed_bs_chart.png', 
              'Figure 7: Debt Issued, Fed B/S, Bank Reserves, Net Liquidity, MOVE Index, SPX, VIX Index')

# 8. Fed Aggregate Credit Chart
pdf.add_page()
pdf.chapter_title('8. Fed Aggregate Credit and Market Indicators')
pdf.chapter_body(
    'This chart examines the relationship between the Federal Reserve\'s aggregate credit, the S&P 500, the VIX volatility index (inverted), '
    'Bitcoin, gold, and the US Dollar Index (DXY) (inverted). The chart demonstrates how changes in Fed credit policies '
    'can influence various asset classes across different markets.'
)
pdf.add_chart('charts/fed_aggregate_credit_chart.png', 
              'Figure 8: Fed Aggregate Credit, SPX, VIX (Inv), Bitcoin, Gold, DXY (inv), weekly series')

# Conclusion
pdf.add_page()
pdf.chapter_title('Conclusion')
pdf.chapter_body(
    'The charts presented in this report demonstrate the complex interrelationships between various financial and economic indicators. '
    'Of particular note is the predictive relationship shown in the Modified Correlation Coefficient between the S&P 500 and VIX, '
    'which has been shifted forward by 4 weeks to highlight its potential for anticipating market movements. '
    'The analysis of Fed balance sheet components, debt issuance, and market indicators provides insights into how monetary and fiscal '
    'policies influence asset prices and market volatility. '
    'These relationships can be valuable for understanding market dynamics and developing investment strategies.'
)

# Save the PDF
pdf_path = 'report/financial_charts_final_combined_report.pdf'
pdf.output(pdf_path)

print(f"Final combined report saved to {pdf_path}")
print("Done!")
